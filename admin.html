<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员控制台 - 角色</title>
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>⭐</text></svg>">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 25%, #667eea 75%, #764ba2 100%);
            min-height: 100vh;
            color: #ffffff;
            overflow-x: hidden;
            position: relative;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(168, 237, 234, 0.5));
            border-radius: 10px;
            border: 2px solid transparent;
            background-clip: content-box;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.5), rgba(168, 237, 234, 0.7));
        }

        /* 背景动画效果 */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            overflow: hidden;
        }

        .floating-orb {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float-orb 15s ease-in-out infinite;
        }

        .floating-orb:nth-child(1) {
            width: 100px;
            height: 100px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-orb:nth-child(2) {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 10%;
            animation-delay: 5s;
        }

        .floating-orb:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 20%;
            left: 20%;
            animation-delay: 10s;
        }

        @keyframes float-orb {
            0%, 100% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.3; }
            33% { transform: translateY(-30px) translateX(20px) scale(1.1); opacity: 0.6; }
            66% { transform: translateY(-10px) translateX(-15px) scale(0.9); opacity: 0.4; }
        }

        /* 粒子效果 */
        .particle-system {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 2;
        }

        .admin-particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(255, 255, 255, 0.4);
            border-radius: 50%;
            animation: particle-float 8s linear infinite;
        }

        @keyframes particle-float {
            0% { transform: translateY(100vh) translateX(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-10vh) translateX(100px); opacity: 0; }
        }

        /* 登录界面优化 */
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            position: relative;
            z-index: 10;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 50px;
            width: 100%;
            max-width: 450px;
            text-align: center;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
            animation: loginCardEntrance 1s ease-out;
        }

        @keyframes loginCardEntrance {
            from { transform: scale(0.9) translateY(30px); opacity: 0; }
            to { transform: scale(1) translateY(0); opacity: 1; }
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
            transform: translateX(-100%);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 90%, 100% { transform: translateX(-100%); }
            10%, 80% { transform: translateX(100%); }
        }

        .login-title {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #ffffff, #a8edea, #fed6e3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: titleGlow 3s ease-in-out infinite;
        }

        @keyframes titleGlow {
            0%, 100% { filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3)); }
            50% { filter: drop-shadow(0 0 20px rgba(168, 237, 234, 0.6)); }
        }

        .login-subtitle {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 40px;
            animation: fadeInUp 0.8s ease-out 0.3s both;
        }

        @keyframes fadeInUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .form-group {
            margin-bottom: 25px;
            text-align: left;
            position: relative;
            animation: fadeInUp 0.8s ease-out 0.5s both;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        .form-input {
            width: 100%;
            padding: 18px 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
            position: relative;
        }

        .form-input:focus {
            outline: none;
            border-color: rgba(168, 237, 234, 0.6);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 25px rgba(168, 237, 234, 0.2);
            transform: translateY(-2px);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        /* 修复select下拉框选项文字颜色 */
        .form-input option {
            background: #2c3e50;
            color: #ffffff;
            padding: 8px;
        }

        .form-input option:hover {
            background: #34495e;
        }

        /* 按钮样式优化 */
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            text-transform: none;
            letter-spacing: 0.3px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            min-height: 36px;
            white-space: nowrap;
            text-decoration: none;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        /* 按钮尺寸变体 */
        .btn-sm {
            padding: 8px 14px;
            font-size: 12px;
            min-height: 32px;
        }

        .btn-lg {
            padding: 16px 28px;
            font-size: 16px;
            min-height: 48px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-primary.btn-full {
            width: 100%;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-1px);
        }

        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a6f);
            color: white;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        .btn-danger:hover {
            background: linear-gradient(45deg, #ff5252, #e53e5c);
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
        }

        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #ff9800);
            color: #333;
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }

        .btn-warning:hover {
            background: linear-gradient(45deg, #ffb300, #ff8f00);
            transform: translateY(-1px);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        /* 按钮组样式 */
        .btn-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            align-items: center;
        }

        .btn-group.btn-group-center {
            justify-content: center;
        }

        .btn-group.btn-group-end {
            justify-content: flex-end;
        }

        /* 表格操作按钮组 */
        .table-actions {
            display: flex;
            gap: 4px;
            align-items: center;
            flex-wrap: wrap;
        }

        .table-actions .btn {
            padding: 6px 10px;
            font-size: 12px;
            min-height: 28px;
        }

        /* 主界面优化 */
        .admin-container {
            display: none;
            min-height: 100vh;
            position: relative;
            z-index: 10;
            animation: adminEntrance 0.8s ease-out;
        }

        @keyframes adminEntrance {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .admin-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 25px 35px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .admin-title {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(45deg, #ffffff, #a8edea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .admin-nav {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .nav-item {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .nav-item:hover::before {
            left: 100%;
        }

        .nav-item:hover,
        .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            border-color: rgba(168, 237, 234, 0.5);
        }

        .nav-item.active {
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));
        }

        .admin-content {
            padding: 40px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
            animation: sectionFadeIn 0.6s ease-out;
        }

        @keyframes sectionFadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 卡片样式优化 */
        .card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
            border-color: rgba(255, 255, 255, 0.25);
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2, #a8edea);
            opacity: 0.8;
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-header .card-title {
            margin-bottom: 0;
        }

        /* 表格样式优化 */
        .table-container {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .table-container table {
            width: 100%;
            border-collapse: collapse;
        }

        .table-container thead {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table-container th {
            padding: 14px 12px;
            text-align: left;
            font-weight: 600;
            color: #ffffff;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .table-container th:last-child {
            border-right: none;
        }

        .table-container tbody tr {
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            transition: background-color 0.2s ease;
        }

        .table-container tbody tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .table-container tbody tr:last-child {
            border-bottom: none;
        }

        .table-container td {
            padding: 12px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            line-height: 1.4;
            border-right: 1px solid rgba(255, 255, 255, 0.05);
            vertical-align: middle;
        }

        .table-container td:last-child {
            border-right: none;
        }

        /* 表格内容优化 */
        .table-text {
            max-width: 200px;
            word-break: break-word;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .table-text.multiline {
            white-space: normal;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .status-badge.status-active {
            background: rgba(40, 167, 69, 0.8);
            color: #ffffff;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status-badge.status-inactive {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .status-badge.status-default {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .status-badge.status-system {
            background: rgba(108, 117, 125, 0.2);
            color: #ffffff;
            border: 1px solid rgba(108, 117, 125, 0.3);
        }

        /* 标签组 */
        .tag-group {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }

        .tag {
            background: rgba(255, 255, 255, 0.15);
            color: rgba(255, 255, 255, 0.9);
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 网格布局优化 */
        .grid {
            display: grid;
            gap: 25px;
        }

        .grid-2 {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        .grid-3 {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        .grid-4 {
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        }

        /* 统计卡片优化 */
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 18px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .stat-card:hover::before {
            transform: translateX(100%);
        }

        .stat-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .stat-number {
            font-size: 42px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 8px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #ffffff, #a8edea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
        }

        /* 配置表单优化 */
        .config-group {
            margin-bottom: 35px;
            position: relative;
        }

        .config-group-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: rgba(255, 255, 255, 0.9);
            padding-bottom: 10px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .config-group-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 50px;
            height: 2px;
            background: linear-gradient(45deg, #667eea, #a8edea);
        }

        /* 开关样式优化 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 70px;
            height: 40px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            transition: .4s;
            border-radius: 40px;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 30px;
            width: 30px;
            left: 3px;
            bottom: 3px;
            background: white;
            transition: .4s;
            border-radius: 50%;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        input:checked + .slider {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-color: rgba(168, 237, 234, 0.5);
        }

        input:checked + .slider:before {
            transform: translateX(30px);
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        /* 文本域优化 */
        .textarea {
            width: 100%;
            min-height: 140px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
            transition: all 0.3s ease;
            line-height: 1.6;
        }

        .textarea:focus {
            outline: none;
            border-color: rgba(168, 237, 234, 0.6);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 25px rgba(168, 237, 234, 0.2);
        }

        /* 消息提示优化 */
        .error-message, .success-message, .warning-message {
            border-radius: 12px;
            padding: 15px 20px;
            margin-top: 15px;
            font-weight: 500;
            animation: messageSlideIn 0.3s ease-out;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        @keyframes messageSlideIn {
            from { transform: translateY(-10px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .error-message {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid rgba(255, 107, 107, 0.5);
            color: #ffcdd2;
        }

        .error-message::before {
            content: '❌';
            font-size: 16px;
        }

        .success-message {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            color: #c8e6c9;
        }

        .success-message::before {
            content: '✅';
            font-size: 16px;
        }

        .warning-message {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.5);
            color: #fff3cd;
        }

        .warning-message::before {
            content: '⚠️';
            font-size: 16px;
        }

        /* 对话框样式优化 */
        .dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: dialogFadeIn 0.3s ease-out;
        }

        @keyframes dialogFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .dialog-content {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 20px;
            padding: 30px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            animation: dialogSlideIn 0.3s ease-out;
        }

        @keyframes dialogSlideIn {
            from { 
                transform: scale(0.9) translateY(30px); 
                opacity: 0; 
            }
            to { 
                transform: scale(1) translateY(0); 
                opacity: 1; 
            }
        }

        .dialog-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 24px;
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            padding-bottom: 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dialog-body {
            margin-bottom: 24px;
        }

        .dialog-footer {
            display: flex;
            justify-content: center;
            gap: 12px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 表单样式优化 */
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }

        .form-row.single {
            grid-template-columns: 1fr;
        }

        .form-section {
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.03);
        }

        .form-section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .dialog-content {
                width: 95%;
                padding: 20px;
                margin: 10px;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .btn-group {
                flex-direction: column;
                align-items: stretch;
            }

            .table-actions {
                flex-direction: column;
                gap: 2px;
            }

            .table-actions .btn {
                width: 100%;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .dialog-title {
                font-size: 20px;
            }

            .card-header {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;
            }

            .card-header .btn-group {
                justify-content: center;
            }
        }

        /* 返回按钮优化 */
        .back-btn {
            position: fixed;
            bottom: 25px;  
            left: 25px;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            color: white;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        /* 加载动画优化 */
        .loading {
            display: inline-block;
            width: 24px;
            height: 24px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 状态指示器 */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            border-radius: 20px;
            font-size: 14px;
            color: #c8e6c9;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 响应式设计优化 */
        @media (max-width: 1024px) {
            .admin-content {
                padding: 30px;
            }
            
            .card {
                padding: 25px;
            }
        }

        @media (max-width: 768px) {

            .admin-header {
                flex-direction: column;
                gap: 20px;
                padding: 20px;
            }

            .admin-nav {
                width: 100%;
                justify-content: center;
            }

            .nav-item {
                flex: 1;
                text-align: center;
                min-width: 80px;
                padding: 10px 16px;
                font-size: 13px;
            }

            .admin-content {
                padding: 20px;
            }

            .grid-2,
            .grid-3,
            .grid-4 {
                grid-template-columns: 1fr;
            }

            .card {
                padding: 20px;
                margin-bottom: 20px;
            }

            .card-title {
                font-size: 20px;
            }

            .stat-number {
                font-size: 32px;
            }

            .login-card {
                padding: 30px;
                margin: 10px;
            }

            .login-title {
                font-size: 28px;
            }

            .back-btn {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 20px;
                display: inline-block;
            }

            .bg-animation {
                display: none; /* 移动端隐藏背景动画以提升性能 */
            }
        }

        @media (max-width: 480px) {
            .admin-nav {
                flex-direction: column;
            }

            .nav-item {
                flex: none;
                width: 100%;
            }

            .form-input, .textarea {
                padding: 15px;
            }

            .btn {
                padding: 15px 25px;
            }

            .stat-card {
                padding: 20px;
            }

            .config-group {
                margin-bottom: 25px;
            }

            .toggle-switch {
                width: 60px;
                height: 35px;
            }

            .slider:before {
                height: 27px;
                width: 27px;
            }

            input:checked + .slider:before {
                transform: translateX(25px);
            }
        }

        /* 暗色模式适配 */
        @media (prefers-color-scheme: dark) {
            .form-input::placeholder {
                color: rgba(255, 255, 255, 0.4);
            }
        }

        /* 减少动画（无障碍） */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* 高对比度模式 */
        @media (prefers-contrast: high) {
            .card, .login-card {
                border: 2px solid rgba(255, 255, 255, 0.5);
            }
            
            .btn-primary {
                border: 2px solid rgba(255, 255, 255, 0.5);
            }
        }

        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            .nav-item:hover,
            .btn:hover,
            .stat-card:hover {
                transform: none;
            }

            .nav-item:active,
            .btn:active,
            .stat-card:active {
                transform: scale(0.98);
            }
        }
    </style>
</head>
<body>
    <!-- 背景动画 -->
    <div class="bg-animation">
        <div class="floating-orb"></div>
        <div class="floating-orb"></div>
        <div class="floating-orb"></div>
    </div>

    <!-- 粒子系统 -->
    <div class="particle-system" id="particleSystem"></div>

    <a href="index.html" class="back-btn">← 返回首页</a>

    <!-- 登录界面 -->
    <div class="login-container" id="loginContainer">
        <div class="login-card">
            <h1 class="login-title">管理员登录</h1>
            <p class="login-subtitle">请输入管理员密码以继续</p>
            
            <form id="loginForm">
                <div class="form-group">
                    <label class="form-label">管理员密码</label>
                    <input type="password" class="form-input" id="password" placeholder="请输入管理员密码" required autocomplete="current-password">
                </div>
                
                <button type="submit" class="btn btn-primary" id="loginBtn">
                    <span id="loginBtnText">登录系统</span>
                    <span id="loginBtnLoading" class="loading" style="display: none;"></span>
                </button>
                
                <div id="loginError" class="error-message" style="display: none;"></div>
            </form>
        </div>
    </div>

    <!-- 管理员主界面 -->
    <div class="admin-container" id="adminContainer">
        <header class="admin-header">
            <h1 class="admin-title">🛡️ 管理员控制台</h1>
            <nav class="admin-nav">
                <div class="nav-item active" onclick="switchTab('stats', event)">📊 统计数据</div>
                <div class="nav-item" onclick="switchTab('characters', event)">🤖 角色管理</div>
                <div class="nav-item" onclick="switchTab('prompts', event)">💬 提示词管理</div>
                <div class="nav-item" onclick="switchTab('config', event)">⚙️ 系统配置</div>
                <div class="nav-item" onclick="switchTab('images', event)">🖼️ 图片管理</div>
                <div class="nav-item" onclick="logout()">🚪 退出登录</div>
            </nav>
        </header>

        <main class="admin-content">
            <!-- 统计数据页面 -->
            <section class="content-section active" id="statsSection">
                <div class="card">
                    <h2 class="card-title">📊 服务运行统计</h2>
                    <div class="grid grid-4">
                        <div class="stat-card">
                            <div class="stat-number" id="totalRequests">-</div>
                            <div class="stat-label">总请求数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="successfulRequests">-</div>
                            <div class="stat-label">成功请求</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="failedRequests">-</div>
                            <div class="stat-label">失败请求</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="reviewBlocked">-</div>
                            <div class="stat-label">审查拦截</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h2 class="card-title">🔧 系统状态监控</h2>
                    <div class="grid grid-2">
                        <div>
                            <p style="margin-bottom: 15px;"><strong>服务启动时间:</strong> <span id="startTime">-</span></p>
                            <p style="margin-bottom: 15px;"><strong>运行状态:</strong> 
                                <span class="status-indicator">
                                    <span class="status-dot"></span>
                                    正常运行
                                </span>
                            </p>
                            <p><strong>API版本:</strong> v1.0.0</p>
                        </div>
                        <div style="text-align: right; display: flex; align-items: center; justify-content: flex-end;">
                            <button class="btn btn-danger" onclick="resetStats()">🔄 重置统计数据</button>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h2 class="card-title">📈 实时监控</h2>
                    <div class="grid grid-3">
                        <div class="stat-card">
                            <div class="stat-number" id="successRate">-</div>
                            <div class="stat-label">成功率 (%)</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="blockRate">-</div>
                            <div class="stat-label">拦截率 (%)</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="uptime">-</div>
                            <div class="stat-label">运行时长</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 图片管理页面 -->
            <section class="content-section" id="imagesSection">
                <div class="card">
                    <h2 class="card-title">🖼️ 图片存储统计</h2>
                    <div class="grid grid-4">
                        <div class="stat-card">
                            <div class="stat-number" id="totalImages">-</div>
                            <div class="stat-label">图片总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalImageSize">-</div>
                            <div class="stat-label">总大小(MB)</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="imageTypes">-</div>
                            <div class="stat-label">文件类型</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="storageUsage">-</div>
                            <div class="stat-label">存储占用</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                    <h2 class="card-title">🗂️ 图片文件管理</h2>
                        <div class="btn-group">
                            <button class="btn btn-secondary btn-sm" onclick="refreshImageList()">🔄 刷新</button>
                        </div>
                    </div>

                    <div class="btn-group" style="margin-bottom: 20px;">
                        <button class="btn btn-secondary" onclick="toggleSelectAllImages()" id="selectAllImagesBtn">☑️ 全选</button>
                        <button class="btn btn-danger" onclick="batchDeleteImages()" id="batchDeleteImagesBtn" disabled>🗑️ 批量删除</button>
                        <button class="btn btn-secondary" onclick="batchDownloadImages()" id="batchDownloadBtn" disabled>📥 批量下载</button>
                    </div>
                    
                    <div class="btn-group" style="margin-bottom: 20px;">
                        <input type="text" class="form-input" id="imageSearchInput" placeholder="搜索图片文件名..." 
                               style="flex: 1; max-width: 300px;" 
                               oninput="filterImages()">
                        <select class="form-input" id="imageTypeFilter" style="min-width: 150px;" onchange="filterImages()">
                            <option value="">所有类型</option>
                            <option value=".jpg">.jpg</option>
                            <option value=".jpeg">.jpeg</option>
                            <option value=".png">.png</option>
                            <option value=".gif">.gif</option>
                            <option value=".webp">.webp</option>
                        </select>
                    </div>

                    <div class="table-container" style="max-height: 600px; overflow-y: auto;">
                        <table id="imagesTable">
                            <thead>
                                <tr>
                                    <th style="width: 50px;">
                                        <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAllImages()">
                                    </th>
                                    <th style="width: 80px;">预览</th>
                                    <th>文件名</th>
                                    <th style="width: 100px;">大小</th>
                                    <th style="width: 150px;">创建时间</th>
                                    <th style="width: 140px;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="imagesTableBody">
                                <!-- 动态生成 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div id="imageMessage" style="display: none; margin-top: 15px;"></div>
                </div>
            </section>

            <!-- 角色管理页面 -->
            <section class="content-section" id="charactersSection">
                <div class="card">
                    <div class="card-header">
                    <h2 class="card-title">🤖 AI角色管理</h2>
                        <div class="btn-group">
                            <button class="btn btn-secondary btn-sm" onclick="refreshCharacterList()">🔄 刷新</button>
                            <button class="btn btn-primary" onclick="showCreateCharacterDialog()">➕ 创建角色</button>
                        </div>
                    </div>

                    <div class="btn-group" style="margin-bottom: 20px;">
                        <button class="btn btn-secondary" onclick="exportAllCharacters()">📤 导出全部</button>
                        <button class="btn btn-secondary" onclick="showImportCharacterDialog()">📥 导入角色</button>
                        <button class="btn btn-warning" onclick="showCharactersPricingSummary()">💰 计费摘要</button>
                    </div>
                    
                    <div class="table-container" style="max-height: 600px; overflow-y: auto;">
                        <table id="charactersTable">
                            <thead>
                                <tr>
                                    <th style="min-width: 120px;">角色名称</th>
                                    <th style="min-width: 120px;">显示名称</th>
                                    <th style="min-width: 140px;">后端模型</th>
                                    <th style="min-width: 100px;">提示词</th>
                                    <th style="min-width: 120px;">计费模式</th>
                                    <th style="min-width: 80px;">状态</th>
                                    <th style="min-width: 200px;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="charactersTableBody">
                                <!-- 动态生成 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div id="characterMessage" style="display: none; margin-top: 15px;"></div>
                </div>
            </section>

            <!-- 提示词管理页面 -->
            <section class="content-section" id="promptsSection">
                <div class="card">
                    <div class="card-header">
                    <h2 class="card-title">💬 提示词管理</h2>
                        <div class="btn-group">
                            <button class="btn btn-secondary btn-sm" onclick="refreshPromptList()">🔄 刷新</button>
                            <button class="btn btn-primary" onclick="showCreatePromptDialog()">➕ 创建提示词</button>
                        </div>
                    </div>

                    <div class="btn-group" style="margin-bottom: 20px;">
                        <button class="btn btn-secondary" onclick="exportAllPrompts()">📤 导出全部</button>
                        <button class="btn btn-secondary" onclick="showImportPromptDialog()">📥 导入提示词</button>
                    </div>
                    
                    <div class="table-container" style="max-height: 600px; overflow-y: auto;">
                        <table id="promptsTable">
                            <thead>
                                <tr>
                                    <th style="min-width: 150px;">提示词名称</th>
                                    <th style="min-width: 200px;">描述</th>
                                    <th style="min-width: 100px;">分类</th>
                                    <th style="min-width: 120px;">标签</th>
                                    <th style="min-width: 80px;">作者</th>
                                    <th style="min-width: 200px;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="promptsTableBody">
                                <!-- 动态生成 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div id="promptMessage" style="display: none; margin-top: 15px;"></div>
                </div>
            </section>

            <!-- 系统配置页面 -->
            <section class="content-section" id="configSection">
                <div class="card">
                    <h2 class="card-title">⚙️ 系统配置管理</h2>
                    <form id="configForm">
                        <div class="config-group">
                            <div class="config-group-title">🔐 管理员设置</div>
                            <div class="form-group">
                                <label class="form-label">管理员密码</label>
                                <input type="password" class="form-input" id="adminPassword" placeholder="输入新密码（留空保持不变）" autocomplete="new-password">
                            </div>
                        </div>

                        <div class="config-group">
                            <div class="config-group-title">🛡️ 内容审查设置</div>
                            <div class="form-group">
                                <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                    启用内容审查
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="reviewEnabled">
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                        </div>

                        <div class="config-group">
                            <div class="config-group-title">🔍 审查API配置</div>
                            <div class="grid grid-2">
                                <div class="form-group">
                                    <label class="form-label">API服务地址</label>
                                    <input type="url" class="form-input" id="reviewApiUrl" placeholder="https://api.example.com/v1">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">API密钥</label>
                                    <input type="password" class="form-input" id="reviewApiKey" placeholder="sk-..." autocomplete="off">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">使用模型</label>
                                <input type="text" class="form-input" id="reviewModel" placeholder="gpt-3.5-turbo">
                            </div>
                        </div>

                        <div class="config-group">
                            <div class="config-group-title">🚀 主服务API配置</div>
                            <div class="grid grid-2">
                                <div class="form-group">
                                    <label class="form-label">API服务地址</label>
                                    <input type="url" class="form-input" id="mainApiUrl" placeholder="https://api.example.com/v1">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">API密钥</label>
                                    <input type="password" class="form-input" id="mainApiKey" placeholder="sk-..." autocomplete="off">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">使用模型</label>
                                <input type="text" class="form-input" id="mainModel" placeholder="gpt-4">
                            </div>
                        </div>

                        <div class="config-group">
                            <div class="config-group-title">📝 总结API配置</div>
                            <div class="form-group">
                                <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                    启用历史对话总结功能
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="summaryEnabled">
                                        <span class="slider"></span>
                                    </label>
                                </label>
                                <small style="color: rgba(255,255,255,0.6); font-size: 12px; margin-top: 5px; display: block;">
                                    使用低成本模型自动总结历史对话内容
                                </small>
                            </div>
                            <div class="grid grid-2">
                                <div class="form-group">
                                    <label class="form-label">API服务地址</label>
                                    <input type="url" class="form-input" id="summaryApiUrl" placeholder="https://api.example.com/v1">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">API密钥</label>
                                    <input type="password" class="form-input" id="summaryApiKey" placeholder="sk-..." autocomplete="off">
                                </div>
                            </div>
                            <div class="grid grid-2">
                                <div class="form-group">
                                    <label class="form-label">使用模型</label>
                                    <input type="text" class="form-input" id="summaryModel" placeholder="gpt-4.1-mini">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">最大Token数</label>
                                    <input type="number" class="form-input" id="summaryMaxTokens" placeholder="2048" min="512" max="8192">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">创意度 (0.0-2.0)</label>
                                <input type="number" class="form-input" id="summaryTemperature" placeholder="0.3" min="0" max="2" step="0.1">
                                <small style="color: rgba(255,255,255,0.6); font-size: 12px; margin-top: 5px; display: block;">
                                    较低的值产生更一致和聚焦的总结
                                </small>
                            </div>
                        </div>

                        <div class="config-group">
                            <div class="config-group-title">🤖 AI角色设定</div>
                            <div class="form-group">
                                <label class="form-label">系统提示词</label>
                                <textarea class="textarea" id="systemPrompt" placeholder="你是灵星逸..."></textarea>
                            </div>
                        </div>

                        <div class="config-group">
                            <div class="config-group-title">🔍 内容审查规则</div>
                            <div class="form-group">
                                <label class="form-label">审查提示词</label>
                                <textarea class="textarea" id="reviewPrompt" placeholder="请审查以下内容..."></textarea>
                            </div>
                        </div>

                        <div class="config-group">
                            <div class="config-group-title">🔑 API访问控制</div>
                            <div class="form-group">
                                <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                    启用API密钥验证
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="requireAuth">
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="form-label">API服务地址 (供用户使用)</label>
                                <input type="url" class="form-input" id="apiBaseUrl" placeholder="https://your-proxy-domain/v1" readonly>
                            </div>
                            <div class="form-group">
                                <label class="form-label">API密钥 (供用户使用)</label>
                                <div style="display: flex; gap: 10px;">
                                    <input type="text" class="form-input" id="apiKey" placeholder="sk-..." readonly style="flex: 1;">
                                    <button type="button" class="btn btn-secondary" onclick="showCurrentApiKey()" style="white-space: nowrap;">👁️ 查看当前</button>
                                    <button type="button" class="btn btn-secondary" onclick="generateNewApiKey()" style="white-space: nowrap;">🔄 重新生成</button>
                                </div>
                                <small style="color: rgba(255,255,255,0.6); font-size: 12px; margin-top: 5px; display: block;">
                                    用户需要使用此密钥来访问API服务
                                </small>
                            </div>
                        </div>
                        
                        <div class="config-group">
                            <div class="config-group-title">👥 客户端访问控制</div>
                            <div class="form-group">
                                <label class="form-label">客户端登录密码</label>
                                <div style="display: flex; gap: 10px; align-items: center;">
                                    <input type="password" class="form-input" id="clientPassword" placeholder="输入新密码（留空保持不变）" autocomplete="new-password" style="flex: 1;">
                                    <button type="button" class="btn btn-secondary" onclick="showCurrentClientPassword()" style="white-space: nowrap;">👁️ 查看当前</button>
                                </div>
                                <small style="color: rgba(255,255,255,0.6); font-size: 12px; margin-top: 5px; display: block;">
                                    用户需要使用此密码访问聊天界面
                                </small>
                            </div>
                            <div class="grid grid-2">
                                <div class="form-group">
                                    <label class="form-label">会话过期时间(小时)</label>
                                    <input type="number" class="form-input" id="clientSessionExpire" min="1" max="168" placeholder="24">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">最大历史消息数</label>
                                    <input type="number" class="form-input" id="clientMaxHistory" min="10" max="99999" placeholder="100">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                    启用聊天历史记录
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="clientEnableHistory">
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                        </div>

                        <div class="config-group">
                            <div class="config-group-title">🤖 模型配置管理</div>
                            <div class="form-group">
                                <label class="form-label">可用模型配置</label>
                                <div id="modelConfigContainer">
                                    <!-- 动态生成模型配置项 -->
                                </div>
                                <button type="button" class="btn btn-secondary" onclick="addModelConfig()" style="margin-top: 10px;">
                                    ➕ 添加新模型
                                </button>
                            </div>
                        </div>

                        <div class="config-group">
                            <div class="config-group-title">🧠 上下文管理配置</div>
                            <div class="form-group">
                                <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                    启用上下文自动裁剪
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="enableContextTrim">
                                        <span class="slider"></span>
                                    </label>
                                </label>
                                <small style="color: rgba(255,255,255,0.6); font-size: 12px; margin-top: 5px; display: block;">
                                    当对话过长时自动裁剪上下文以节省Token。关闭此选项时，所有对话历史将完整保留
                                </small>
                            </div>
                            <div class="grid grid-2">
                                <div class="form-group">
                                    <label class="form-label">最大历史轮次</label>
                                    <input type="number" class="form-input" id="maxHistoryTurns" min="3" max="50" placeholder="10">
                                    <small style="color: rgba(255,255,255,0.6); font-size: 12px; margin-top: 5px; display: block;">
                                        保留的最大对话轮数（一问一答为一轮）
                                    </small>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">最大上下文Token数</label>
                                    <input type="number" class="form-input" id="maxContextTokens" min="1000" max="100000" placeholder="30000">
                                    <small style="color: rgba(255,255,255,0.6); font-size: 12px; margin-top: 5px; display: block;">
                                        上下文允许的最大Token数量（根据模型能力调整）
                                    </small>
                                </div>
                            </div>
                            <div class="grid grid-2">
                                <div class="form-group">
                                    <label class="form-label">Token缓冲区</label>
                                    <input type="number" class="form-input" id="tokenBuffer" min="100" max="5000" placeholder="1000">
                                    <small style="color: rgba(255,255,255,0.6); font-size: 12px; margin-top: 5px; display: block;">
                                        为模型响应预留的Token数量
                                    </small>
                                </div>
                                <div class="form-group">
                                    <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                        始终保留系统提示词
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="alwaysKeepSystem">
                                            <span class="slider"></span>
                                        </label>
                                    </label>
                                    <small style="color: rgba(255,255,255,0.6); font-size: 12px; margin-top: 5px; display: block;">
                                        上下文裁剪时始终保留系统提示词
                                    </small>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">上下文裁剪策略</label>
                                <select class="form-input" id="contextTrimStrategy">
                                    <option value="sliding_window">滑动窗口（保留最近的对话）</option>
                                    <option value="token_priority">Token优先（保留短消息）</option>
                                    <option value="first_in_first_out">先进先出（保留新消息）</option>
                                </select>
                                <small style="color: rgba(255,255,255,0.6); font-size: 12px; margin-top: 5px; display: block;">
                                    当上下文超出限制时使用的裁剪策略
                                </small>
                            </div>
                        </div>

                        <div class="config-group">
                            <div class="config-group-title">💾 消息备份配置</div>
                            <div class="form-group">
                                <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                    启用消息记录备份
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="conversationBackupEnabled">
                                        <span class="slider"></span>
                                    </label>
                                </label>
                                <small style="color: rgba(255,255,255,0.6); font-size: 12px; margin-top: 5px; display: block;">
                                    备份用户对话内容到本地存储，用于数据统计和分析
                                </small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">备份类型</label>
                                <select class="form-input" id="conversationBackupType">
                                    <option value="smart">智能备份（推荐）</option>
                                    <option value="legacy">传统备份</option>
                                    <option value="disabled">禁用备份</option>
                                </select>
                                <small style="color: rgba(255,255,255,0.6); font-size: 12px; margin-top: 5px; display: block;">
                                    智能备份具有去重和压缩功能，占用存储空间更小
                                </small>
                            </div>
                            <div class="grid grid-2">
                                <div class="form-group">
                                    <label class="form-label">备份保留天数</label>
                                    <input type="number" class="form-input" id="conversationBackupCleanupDays" min="1" max="365" placeholder="30">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">最大备份大小(MB)</label>
                                    <input type="number" class="form-input" id="conversationBackupMaxSize" min="100" max="10240" placeholder="1024">
                                </div>
                            </div>
                            <div class="grid grid-2">
                                <div class="form-group">
                                    <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                        启用每日统计
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="conversationBackupDailyStats">
                                            <span class="slider"></span>
                                        </label>
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                        启用会话索引
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="conversationBackupSessionIndex">
                                            <span class="slider"></span>
                                        </label>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="config-group">
                            <div class="config-group-title">⚡ 重试策略配置</div>
                            <div class="grid grid-2">
                                <div class="form-group">
                                    <label class="form-label">最大重试次数</label>
                                    <input type="number" class="form-input" id="maxRetries" min="0" max="10" placeholder="3">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">基础延迟(秒)</label>
                                    <input type="number" class="form-input" id="baseDelay" min="0.1" max="10" step="0.1" placeholder="1.0">
                                </div>
                            </div>
                            <div class="grid grid-2">
                                <div class="form-group">
                                    <label class="form-label">最大延迟(秒)</label>
                                    <input type="number" class="form-input" id="maxDelay" min="1" max="60" step="0.5" placeholder="10.0">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">指数退避底数</label>
                                    <input type="number" class="form-input" id="exponentialBase" min="1.1" max="5" step="0.1" placeholder="2">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                    添加随机抖动
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="jitterEnabled">
                                        <span class="slider"></span>
                                    </label>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="form-label">可重试状态码 (用逗号分隔)</label>
                                <input type="text" class="form-input" id="retryableStatusCodes" placeholder="429, 500, 502, 503, 504">
                            </div>
                        </div>
                        
                        <div class="dialog-footer">
                            <button type="submit" class="btn btn-primary btn-lg" id="saveConfigBtn">
                                <span id="saveConfigText">💾 保存配置</span>
                                <span id="saveConfigLoading" class="loading" style="display: none;"></span>
                            </button>
                        </div>

                        <div id="configMessage" style="display: none;"></div>
                    </form>
                </div>
            </section>
        </main>
    </div>

    <script>
        // 令牌管理
        let accessToken = '';
        let refreshToken = '';
        let tokenRefreshTimer = null;

        function saveTokensToStorage(access, refresh, expiresIn) {
            accessToken = access;
            refreshToken = refresh;
            localStorage.setItem('admin_access_token', access);
            localStorage.setItem('admin_refresh_token', refresh);
            localStorage.setItem('admin_token_expires_at', Date.now() + (expiresIn * 1000));
            setupTokenRefresh(expiresIn);
        }

        function loadTokensFromStorage() {
            const savedAccessToken = localStorage.getItem('admin_access_token');
            const savedRefreshToken = localStorage.getItem('admin_refresh_token');
            const expiresAt = localStorage.getItem('admin_token_expires_at');

            if (savedAccessToken && savedRefreshToken && expiresAt) {
                const now = Date.now();
                const timeLeftMs = parseInt(expiresAt) - now;

                if (timeLeftMs > 0) { // 令牌未过期
                    accessToken = savedAccessToken;
                    refreshToken = savedRefreshToken;
                    setupTokenRefresh(Math.floor(timeLeftMs / 1000));
                    return true;
                }
            }
            clearTokensFromStorage(); // 清除过期的或无效的令牌
            return false;
        }

        function clearTokensFromStorage() {
            accessToken = '';
            refreshToken = '';
            localStorage.removeItem('admin_access_token');
            localStorage.removeItem('admin_refresh_token');
            localStorage.removeItem('admin_token_expires_at');
            if (tokenRefreshTimer) {
                clearTimeout(tokenRefreshTimer);
                tokenRefreshTimer = null;
            }
        }

        async function tryRefreshTokenAsync(tokenToUse = null) {
            const currentRefreshToken = tokenToUse || refreshToken;
            if (!currentRefreshToken) {
                console.log("No refresh token available for refresh attempt.");
                return false;
            }

            try {
                const response = await fetch('/admin/refresh', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({refresh_token: currentRefreshToken})
                });

                if (response.ok) {
                    const data = await response.json();
                    saveTokensToStorage(data.access_token, data.refresh_token, data.expires_in);
                    console.log("Token refreshed successfully.");
                    return true;
                } else {
                    console.error('Token refresh failed with status:', response.status);
                    const errorData = await response.json().catch(() => ({detail: "Unknown refresh error"}));
                    console.error('Refresh error details:', errorData.detail);
                    // 如果刷新失败（例如刷新令牌也过期或无效），则登出
                    if (response.status === 401) {
                         logout(false); // 不要再次确认
                    }
                    return false;
                }
            } catch (error) {
                console.error('Error during token refresh:', error);
                return false;
            }
        }

        function setupTokenRefresh(expiresInSeconds) {
            if (tokenRefreshTimer) {
                clearTimeout(tokenRefreshTimer);
            }
            // 尝试在令牌过期前5分钟刷新，但至少在1分钟后执行
            const refreshDelayMs = Math.max((expiresInSeconds - 300) * 1000, 60 * 1000);
            
            console.log(`Setting up token refresh in ${refreshDelayMs / 1000} seconds.`);
            tokenRefreshTimer = setTimeout(async () => {
                console.log("Attempting scheduled token refresh...");
                const success = await tryRefreshTokenAsync();
                if (!success) {
                    console.log("Scheduled token refresh failed. Logging out.");
                    logout(false); // 自动登出，不需确认
                }
            }, refreshDelayMs);
        }

        async function validateTokenOnServer() {
            if (!accessToken) return false;
            try {
                const response = await makeAuthenticatedRequest('/api/status'); // 使用一个轻量级认证端点
                return response.ok;
            } catch (error) {
                console.error('Token validation failed:', error);
                return false;
            }
        }
        
        async function makeAuthenticatedRequest(url, options = {}) {
            if (!accessToken) {
                console.error("No access token available for request:", url);
                logout(false); // No token, so logout
                throw new Error('用户未认证');
            }

            const requestOptions = {
                ...options,
                headers: {
                    ...options.headers,
                    'Authorization': `Bearer ${accessToken}`
                }
            };

            let response = await fetch(url, requestOptions);

            if (response.status === 401) {
                console.log("Access token expired or invalid, attempting refresh for URL:", url);
                const refreshed = await tryRefreshTokenAsync();
                if (refreshed) {
                    console.log("Token refreshed, retrying original request to:", url);
                    requestOptions.headers['Authorization'] = `Bearer ${accessToken}`;
                    response = await fetch(url, requestOptions); // 重试原始请求
                } else {
                    console.log("Token refresh failed, logging out.");
                    logout(false); // 刷新失败，登出
                    throw new Error('认证失败，请重新登录');
                }
            }
            return response;
        }


        // 创建粒子系统
        function createParticleSystem() {
            const particleSystem = document.getElementById('particleSystem');
            if (!particleSystem) return;
            const particleCount = 30;
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'admin-particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (6 + Math.random() * 4) + 's';
                particleSystem.appendChild(particle);
            }
        }

        function calculatePercentage(value, total) {
            return total > 0 ? Math.round((value / total) * 100) : 0;
        }

        function calculateUptime(startTime) {
            if (!startTime) return '-';
            const start = new Date(startTime);
            const now = new Date();
            let diffMs = now - start;

            const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
            diffMs -= days * (1000 * 60 * 60 * 24);
            const hours = Math.floor(diffMs / (1000 * 60 * 60));
            diffMs -= hours * (1000 * 60 * 60);
            const minutes = Math.floor(diffMs / (1000 * 60));

            let uptimeString = '';
            if (days > 0) uptimeString += `${days}天 `;
            if (hours > 0 || days > 0) uptimeString += `${hours}小时 `;
            uptimeString += `${minutes}分钟`;
            return uptimeString.trim() || '刚刚';
        }

        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');
            const loginBtnLoading = document.getElementById('loginBtnLoading');
            const loginError = document.getElementById('loginError');

            loginBtn.disabled = true;
            loginBtnText.style.display = 'none';
            loginBtnLoading.style.display = 'inline-block';
            loginError.style.display = 'none';

            try {
                const response = await fetch('/admin/login', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ password })
                });
                const data = await response.json();
                if (response.ok && data.success) {
                    saveTokensToStorage(data.access_token, data.refresh_token, data.expires_in);
                    document.getElementById('loginContainer').style.display = 'none';
                    document.getElementById('adminContainer').style.display = 'block';
                    loadStats();
                    loadConfig();
                } else {
                    loginError.textContent = data.detail || '登录失败，请检查密码';
                    loginError.style.display = 'block';
                }
            } catch (error) {
                loginError.textContent = '网络连接失败，请检查网络后重试';
                loginError.style.display = 'block';
            }
            loginBtn.disabled = false;
            loginBtnText.style.display = 'inline';
            loginBtnLoading.style.display = 'none';
        });

        function switchTab(tab, event) {
            document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
            if (event && event.currentTarget) {
                 event.currentTarget.classList.add('active');
            } else { // Fallback if event is not passed, e.g. programmatic switch
                const activeNavItem = document.querySelector(`.nav-item[onclick*="'${tab}'"]`);
                if (activeNavItem) activeNavItem.classList.add('active');
            }
            
            document.querySelectorAll('.content-section').forEach(section => section.classList.remove('active'));
            document.getElementById(tab + 'Section').classList.add('active');

            if (tab === 'stats') loadStats();
            else if (tab === 'config') loadConfig();
            else if (tab === 'images') loadImages();
        }

        async function loadStats() {
            try {
                const response = await makeAuthenticatedRequest('/admin/stats');
                if (response.ok) {
                    const stats = await response.json();
                    document.getElementById('totalRequests').textContent = stats.total_requests || 0;
                    document.getElementById('successfulRequests').textContent = stats.successful_requests || 0;
                    document.getElementById('failedRequests').textContent = stats.failed_requests || 0;
                    document.getElementById('reviewBlocked').textContent = stats.review_blocked || 0;
                    const total = stats.total_requests || 0;
                    document.getElementById('successRate').textContent = calculatePercentage(stats.successful_requests || 0, total);
                    document.getElementById('blockRate').textContent = calculatePercentage(stats.review_blocked || 0, total);
                    if (stats.start_time) {
                        document.getElementById('startTime').textContent = new Date(stats.start_time).toLocaleString('zh-CN');
                        document.getElementById('uptime').textContent = calculateUptime(stats.start_time);
                    }
                } else {
                     console.error('加载统计数据失败:', await response.text());
                }
            } catch (error) {
                console.error('加载统计数据时发生错误:', error);
            }
        }


        // 渲染模型配置
        function renderModelConfig(modelConfig) {
            const container = document.getElementById('modelConfigContainer');
            container.innerHTML = '';
            
            Object.entries(modelConfig || {}).forEach(([modelId, modelInfo], index) => {
                const modelDiv = document.createElement('div');
                modelDiv.className = 'model-config-item';
                modelDiv.style.cssText = `
                    background: rgba(255, 255, 255, 0.05);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 10px;
                    padding: 20px;
                    margin-bottom: 15px;
                    position: relative;
                `;
                
                modelDiv.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="margin: 0; color: #fff;">模型 ID: ${modelId}</h4>
                        <button type="button" class="btn btn-danger" onclick="removeModelConfig('${modelId}')" style="padding: 5px 10px; font-size: 12px;">
                            🗑️ 删除
                        </button>
                    </div>
                    <div class="grid grid-2" style="gap: 15px;">
                        <div class="form-group" style="margin-bottom: 10px;">
                            <label class="form-label">显示名称</label>
                            <input type="text" class="form-input" 
                                id="model_${index}_display_name" 
                                value="${modelInfo.display_name || ''}" 
                                placeholder="灵星逸 (标准版)">
                        </div>
                        <div class="form-group" style="margin-bottom: 10px;">
                            <label class="form-label">后端模型</label>
                            <input type="text" class="form-input" 
                                id="model_${index}_backend_model" 
                                value="${modelInfo.backend_model || ''}" 
                                placeholder="gemini-2.5-pro-preview-06-05">
                        </div>
                    </div>
                    <div class="form-group" style="margin-bottom: 0;">
                        <label class="form-label">模型描述</label>
                        <input type="text" class="form-input" 
                            id="model_${index}_description" 
                            value="${modelInfo.description || ''}" 
                            placeholder="标准版本，提供全面的AI助手体验">
                    </div>
                    <input type="hidden" id="model_${index}_id" value="${modelId}">
                `;
                
                container.appendChild(modelDiv);
            });
        }

        // 添加新模型配置
        function addModelConfig() {
            const modelId = prompt('请输入新模型的ID（如：astra-fast）:');
            if (!modelId || !modelId.trim()) return;
            
            const trimmedId = modelId.trim();
            
            // 检查是否已存在
            const currentConfig = getCurrentModelConfig();
            if (currentConfig[trimmedId]) {
                alert('此模型ID已存在！');
                return;
            }
            
            // 添加新模型
            currentConfig[trimmedId] = {
                display_name: `新模型 (${trimmedId})`,
                backend_model: "",
                description: ""
            };
            
            renderModelConfig(currentConfig);
            formChanged = true;
        }

        // 删除模型配置
        function removeModelConfig(modelId) {
            if (!confirm(`确定要删除模型 "${modelId}" 吗？`)) return;
            
            const currentConfig = getCurrentModelConfig();
            delete currentConfig[modelId];
            
            renderModelConfig(currentConfig);
            formChanged = true;
        }

        // 获取当前模型配置
        function getCurrentModelConfig() {
            const container = document.getElementById('modelConfigContainer');
            const config = {};
            
            container.querySelectorAll('.model-config-item').forEach((item, index) => {
                const modelId = item.querySelector(`#model_${index}_id`).value;
                const displayName = item.querySelector(`#model_${index}_display_name`).value;
                const backendModel = item.querySelector(`#model_${index}_backend_model`).value;
                const description = item.querySelector(`#model_${index}_description`).value;
                
                if (modelId) {
                    config[modelId] = {
                        display_name: displayName,
                        backend_model: backendModel,
                        description: description
                    };
                }
            });
            
            return config;
        }

        // 显示当前客户端密码
        async function showCurrentClientPassword() {
            try {
                const response = await makeAuthenticatedRequest('/admin/current-client-password');
                if (response.ok) {
                    const data = await response.json();
                    alert(`当前客户端密码：${data.password}`);
                } else {
                    alert('获取密码失败');
                }
            } catch (error) {
                alert('网络错误');
            }
        }

        // 显示当前API密钥
        async function showCurrentApiKey() {
            try {
                const response = await makeAuthenticatedRequest('/admin/current-api-key');
                if (response.ok) {
                    const data = await response.json();
                    // 显示在弹窗中，并且可以复制
                    const apiKey = data.api_key;
                    if (navigator.clipboard && window.isSecureContext) {
                        // 如果支持剪贴板API，提供复制功能
                        if (confirm(`当前API密钥：\n\n${apiKey}\n\n点击"确定"复制到剪贴板`)) {
                            try {
                                await navigator.clipboard.writeText(apiKey);
                                alert('✅ API密钥已复制到剪贴板！');
                            } catch (err) {
                                console.error('复制失败:', err);
                                alert(`API密钥：${apiKey}\n\n请手动复制`);
                            }
                        }
                    } else {
                        // 如果不支持剪贴板API，只显示
                        alert(`当前API密钥：\n\n${apiKey}\n\n请手动复制`);
                    }
                } else {
                    alert('获取API密钥失败');
                }
            } catch (error) {
                alert('网络错误');
            }
        }

        async function loadConfig() {
            try {
                const response = await makeAuthenticatedRequest('/admin/config');
                if (response.ok) {
                    const config = await response.json();
                    document.getElementById('reviewEnabled').checked = config.review_enabled || false;
                    if (config.review_api) {
                        document.getElementById('reviewApiUrl').value = config.review_api.base_url || '';
                        document.getElementById('reviewApiKey').value = config.review_api.api_key || '';
                        document.getElementById('reviewModel').value = config.review_api.model || '';
                    }
                    if (config.main_api) {
                        document.getElementById('mainApiUrl').value = config.main_api.base_url || '';
                        document.getElementById('mainApiKey').value = config.main_api.api_key || '';
                        document.getElementById('mainModel').value = config.main_api.model || '';
                    }
                    if (config.summary_api) {
                        document.getElementById('summaryEnabled').checked = config.summary_api.enabled !== undefined ? config.summary_api.enabled : false;
                        document.getElementById('summaryApiUrl').value = config.summary_api.base_url || '';
                        document.getElementById('summaryApiKey').value = config.summary_api.api_key || '';
                        document.getElementById('summaryModel').value = config.summary_api.model || '';
                        document.getElementById('summaryMaxTokens').value = config.summary_api.max_tokens || 2048;
                        document.getElementById('summaryTemperature').value = config.summary_api.temperature || 0.3;
                    }
                    if (config.retry_config) {
                        document.getElementById('maxRetries').value = config.retry_config.max_retries !== undefined ? config.retry_config.max_retries : 3;
                        document.getElementById('baseDelay').value = config.retry_config.base_delay !== undefined ? config.retry_config.base_delay : 1.0;
                        document.getElementById('maxDelay').value = config.retry_config.max_delay !== undefined ? config.retry_config.max_delay : 10.0;
                        document.getElementById('exponentialBase').value = config.retry_config.exponential_base !== undefined ? config.retry_config.exponential_base : 2;
                        document.getElementById('jitterEnabled').checked = config.retry_config.jitter !== undefined ? config.retry_config.jitter : true;
                        document.getElementById('retryableStatusCodes').value = (config.retry_config.retryable_status_codes || [429, 500, 502, 503, 504]).join(', ');
                    }
                    if (config.api_config) {
                        document.getElementById('requireAuth').checked = config.api_config.require_auth !== undefined ? config.api_config.require_auth : true;
                        document.getElementById('apiBaseUrl').value = config.api_config.base_url || '';
                        // 不在输入框显示API密钥，只提供查看按钮
                        document.getElementById('apiKey').placeholder = config.api_config.api_key ? '当前已设置API密钥（点击"查看当前"按钮查看）' : '点击"重新生成"按钮生成新密钥';
                    }
                    if (config.client_config) {
                        const clientPassword = config.client_config.password || '';
                        if (clientPassword.includes('*')) {
                            document.getElementById('clientPassword').value = clientPassword;
                            document.getElementById('clientPassword').placeholder = '当前密码已设置（显示为掩码）';
                        } else {
                            document.getElementById('clientPassword').value = '';
                            document.getElementById('clientPassword').placeholder = `当前密码: ${clientPassword}`;
                        }
                        document.getElementById('clientSessionExpire').value = config.client_config.session_expire_hours || 72;
                        document.getElementById('clientMaxHistory').value = config.client_config.max_history_messages || 100;
                        document.getElementById('clientEnableHistory').checked = config.client_config.enable_history !== undefined ? config.client_config.enable_history : true;
                    }
                    if (config.model_config) {
                        renderModelConfig(config.model_config);
                    }
                    // 加载上下文配置
                    if (config.context_config) {
                        document.getElementById('enableContextTrim').checked = config.context_config.enable_context_trim !== undefined ? 
                            config.context_config.enable_context_trim : true;
                        document.getElementById('maxHistoryTurns').value = config.context_config.max_history_turns || 10;
                        document.getElementById('maxContextTokens').value = config.context_config.max_context_tokens || 30000;
                        document.getElementById('tokenBuffer').value = config.context_config.token_buffer || 1000;
                        document.getElementById('alwaysKeepSystem').checked = config.context_config.always_keep_system !== undefined ? 
                            config.context_config.always_keep_system : true;
                        
                        const trimStrategy = document.getElementById('contextTrimStrategy');
                        if (trimStrategy) {
                            trimStrategy.value = config.context_config.context_trim_strategy || 'sliding_window';
                        }
                    }
                    // 加载消息备份配置
                    if (config.conversation_backup) {
                        document.getElementById('conversationBackupEnabled').checked = config.conversation_backup.enabled !== undefined ? 
                            config.conversation_backup.enabled : true;
                        document.getElementById('conversationBackupType').value = config.conversation_backup.backup_type || 'smart';
                        document.getElementById('conversationBackupCleanupDays').value = config.conversation_backup.cleanup_days || 30;
                        document.getElementById('conversationBackupMaxSize').value = Math.round((config.conversation_backup.max_backup_size || 1073741824) / (1024 * 1024));
                        document.getElementById('conversationBackupDailyStats').checked = config.conversation_backup.enable_daily_stats !== undefined ? 
                            config.conversation_backup.enable_daily_stats : true;
                        document.getElementById('conversationBackupSessionIndex').checked = config.conversation_backup.enable_session_index !== undefined ? 
                            config.conversation_backup.enable_session_index : true;
                    }
                    document.getElementById('systemPrompt').value = config.system_prompt || '';
                    document.getElementById('reviewPrompt').value = config.review_prompt || '';
                    formChanged = false;
                } else {
                    console.error('加载配置失败:', await response.text());
                }
            } catch (error) {
                console.error('加载配置时发生错误:', error);
            }
        }

        let formChanged = false;
        document.getElementById('configForm').addEventListener('input', () => {
            formChanged = true;
        });

        document.getElementById('configForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const saveBtn = document.getElementById('saveConfigBtn');
            const saveText = document.getElementById('saveConfigText');
            const saveLoading = document.getElementById('saveConfigLoading');
            const configMessage = document.getElementById('configMessage');
            // 收集模型配置
            const modelConfig = getCurrentModelConfig();
        
            saveBtn.disabled = true;
            saveText.style.display = 'none';
            saveLoading.style.display = 'inline-block';
            configMessage.style.display = 'none';

            const contextConfig = {
                enable_context_trim: document.getElementById('enableContextTrim').checked,
                max_history_turns: parseInt(document.getElementById('maxHistoryTurns').value) || 10,
                max_context_tokens: parseInt(document.getElementById('maxContextTokens').value) || 30000,
                token_buffer: parseInt(document.getElementById('tokenBuffer').value) || 1000,
                always_keep_system: document.getElementById('alwaysKeepSystem').checked,
                context_trim_strategy: document.getElementById('contextTrimStrategy').value || 'sliding_window'
            };

            const retryableStatusCodes = document.getElementById('retryableStatusCodes').value
                .split(',')
                .map(code => parseInt(code.trim()))
                .filter(code => !isNaN(code));

            let newConfigData = {
                review_enabled: document.getElementById('reviewEnabled').checked,
                review_api: {
                    base_url: document.getElementById('reviewApiUrl').value,
                    api_key: document.getElementById('reviewApiKey').value,
                    model: document.getElementById('reviewModel').value
                },
                main_api: {
                    base_url: document.getElementById('mainApiUrl').value,
                    api_key: document.getElementById('mainApiKey').value,
                    model: document.getElementById('mainModel').value
                },
                summary_api: {
                    enabled: document.getElementById('summaryEnabled').checked,
                    base_url: document.getElementById('summaryApiUrl').value,
                    api_key: document.getElementById('summaryApiKey').value,
                    model: document.getElementById('summaryModel').value,
                    max_tokens: parseInt(document.getElementById('summaryMaxTokens').value) || 2048,
                    temperature: isNaN(parseFloat(document.getElementById('summaryTemperature').value)) ? 0.3 : parseFloat(document.getElementById('summaryTemperature').value)
                },
                system_prompt: document.getElementById('systemPrompt').value,
                review_prompt: document.getElementById('reviewPrompt').value,
                retry_config: {
                    max_retries: isNaN(parseInt(document.getElementById('maxRetries').value)) ? 3 : parseInt(document.getElementById('maxRetries').value),
                    base_delay: parseFloat(document.getElementById('baseDelay').value) || 1.0,
                    max_delay: parseFloat(document.getElementById('maxDelay').value) || 10.0,
                    exponential_base: parseFloat(document.getElementById('exponentialBase').value) || 2,
                    jitter: document.getElementById('jitterEnabled').checked,
                    retryable_status_codes: retryableStatusCodes
                },
                api_config: {
                    base_url: document.getElementById('apiBaseUrl').value,
                    api_key: document.getElementById('apiKey').value,
                    require_auth: document.getElementById('requireAuth').checked
                },
                client_config: {
                    session_expire_hours: parseInt(document.getElementById('clientSessionExpire').value) || 24,
                    max_history_messages: parseInt(document.getElementById('clientMaxHistory').value) || 100,
                    enable_history: document.getElementById('clientEnableHistory').checked
                },
                model_config: modelConfig,
                // 添加上下文配置
                context_config: contextConfig,
                // 添加消息备份配置
                conversation_backup: {
                    enabled: document.getElementById('conversationBackupEnabled').checked,
                    backup_type: document.getElementById('conversationBackupType').value,
                    cleanup_days: parseInt(document.getElementById('conversationBackupCleanupDays').value) || 30,
                    max_backup_size: (parseInt(document.getElementById('conversationBackupMaxSize').value) || 1024) * 1024 * 1024,
                    enable_daily_stats: document.getElementById('conversationBackupDailyStats').checked,
                    enable_session_index: document.getElementById('conversationBackupSessionIndex').checked
                }
        
            };
            
            const newPassword = document.getElementById('adminPassword').value;
            if (newPassword.trim()) {
                newConfigData.admin_password = newPassword;
            }

            // 修复客户端密码处理逻辑
            const newClientPassword = document.getElementById('clientPassword').value.trim();
            if (newClientPassword && !newClientPassword.includes('*')) {
                // 只有在输入了新密码且不是掩码时才更新
                newConfigData.client_config.password = newClientPassword;
            }
            // 如果为空或是掩码，不传递password字段，让后端保留现有密码

            try {
                const response = await makeAuthenticatedRequest('/admin/config', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ config: newConfigData })
                });
                const data = await response.json();
                if (response.ok && data.success) {
                    configMessage.textContent = '✅ 配置保存成功！';
                    configMessage.className = 'success-message';
                    document.getElementById('adminPassword').value = '';
                    formChanged = false;
                    // 重新加载配置以获取最新状态
                    await loadConfig();
                } else {
                    configMessage.textContent = '❌ ' + (data.detail || '保存失败，请重试');
                    configMessage.className = 'error-message';
                }
            } catch (error) {
                configMessage.textContent = '❌ 网络错误，请检查连接后重试';
                configMessage.className = 'error-message';
            }
            configMessage.style.display = 'block';
            saveBtn.disabled = false;
            saveText.style.display = 'inline';
            saveLoading.style.display = 'none';
            setTimeout(() => { configMessage.style.display = 'none'; }, 5000);
        });

        async function resetStats() {
            if (!confirm('⚠️ 确定要重置所有统计数据吗？\n\n此操作将清空所有统计记录，且不可撤销。')) return;
            try {
                const response = await makeAuthenticatedRequest('/admin/reset-stats', { method: 'POST' });
                if (response.ok) {
                    loadStats();
                    alert('✅ 统计数据已成功重置');
                } else {
                    const data = await response.json().catch(() => ({}));
                    alert('❌ 重置失败: ' + (data.detail || '请重试'));
                }
            } catch (error) {
                alert('❌ 网络错误，请检查连接后重试');
            }
        }

        async function logout(confirmLogout = true) {
            if (confirmLogout && !confirm('🤔 确定要退出管理员控制台吗？')) return;

            if (accessToken) {
                try {
                    await makeAuthenticatedRequest('/admin/logout', { method: 'POST' });
                } catch (error) {
                    // Log error but proceed with local logout
                    console.error("Error during server logout:", error);
                }
            }
            
            clearTokensFromStorage();
            document.getElementById('adminContainer').style.display = 'none';
            document.getElementById('loginContainer').style.display = 'flex';
            document.getElementById('password').value = '';
            document.querySelectorAll('input[type="password"]').forEach(input => input.value = '');
            formChanged = false; // Reset form changed flag on logout
        }
        
        async function generateNewApiKey() {
            if (!confirm('⚠️ 确定要重新生成API密钥吗？\n\n旧密钥将立即失效，所有使用旧密钥的客户端需要更新！\n\n生成后请务必点击“保存配置”按钮以使更改生效。')) {
                return;
            }
            try {
                const response = await makeAuthenticatedRequest('/admin/generate-api-key', { method: 'POST' });
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('apiKey').value = data.api_key;
                    alert('✅ 新的API密钥已在上方表单中生成！\n请点击页面底部的“保存配置”按钮以应用此更改。');
                    formChanged = true; // Mark form as changed
                } else {
                    const data = await response.json().catch(() => ({}));
                    alert('❌ 生成失败: ' + (data.detail || '请重试'));
                }
            } catch (error) {
                alert('❌ 网络错误，请检查连接后重试');
            }
        }

        document.addEventListener('DOMContentLoaded', async () => {
            createParticleSystem();
            if (loadTokensFromStorage()) {
                const isValid = await validateTokenOnServer();
                if (isValid) {
                    document.getElementById('loginContainer').style.display = 'none';
                    document.getElementById('adminContainer').style.display = 'block';
                    loadStats(); // Load initial stats
                    loadConfig(); // Load initial config
                } else {
                    // Token loaded but invalid on server (e.g. session revoked)
                    clearTokensFromStorage();
                    document.getElementById('loginContainer').style.display = 'flex';
                    document.getElementById('adminContainer').style.display = 'none';
                }
            } else {
                document.getElementById('loginContainer').style.display = 'flex';
                document.getElementById('adminContainer').style.display = 'none';
            }

            setInterval(() => {
                if (accessToken && document.getElementById('statsSection').classList.contains('active')) {
                    loadStats();
                }
                // Update uptime dynamically
                const uptimeElem = document.getElementById('uptime');
                const startTimeStr = document.getElementById('startTime').textContent;
                if (uptimeElem && startTimeStr && startTimeStr !== '-') {
                    // Assuming startTime is already loaded and displayed in a parsable format
                    // This requires startTime to be stored or re-fetched to be accurate
                    // For simplicity, we'll rely on the periodic loadStats for uptime update
                }

            }, 10000); // Refresh stats every 10 seconds
        });

        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        if (accessToken) switchTab('stats', e);
                        break;
                    case '2':
                        e.preventDefault();
                        if (accessToken) switchTab('config', e);
                        break;
                }
            }
             if (e.key === 'Enter' && document.getElementById('loginContainer').style.display !== 'none') {
                if (e.target.form && e.target.form.id === 'loginForm') {
                     e.preventDefault();
                     document.getElementById('loginForm').dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
                }
            }
        });

        window.addEventListener('beforeunload', (e) => {
            if (formChanged) {
                e.preventDefault();
                e.returnValue = '您有未保存的配置更改，确定要离开吗？';
            }
        });

        document.getElementById('adminPassword').addEventListener('input', (e) => {
            const password = e.target.value;
            // Basic visual feedback, actual validation is on backend if strong pass required
            if (password.length > 0 && password.length < 8) { 
                e.target.style.borderColor = 'rgba(255, 107, 107, 0.6)';
            } else {
                e.target.style.borderColor = ''; // Reset to default
            }
        });

        // 图片管理相关变量
        let allImages = [];
        let filteredImages = [];
        let selectedImages = new Set();

        // 加载图片数据
        async function loadImages() {
            try {
                // 从stats中加载图片统计
                const statsResponse = await makeAuthenticatedRequest('/admin/stats');
                if (statsResponse.ok) {
                    const stats = await statsResponse.json();
                    const imageStats = stats.image_stats || {};
                    document.getElementById('totalImages').textContent = imageStats.total_files || 0;
                    document.getElementById('totalImageSize').textContent = imageStats.total_size_mb || 0;
                    document.getElementById('imageTypes').textContent = Object.keys(imageStats.file_types || {}).length;
                    document.getElementById('storageUsage').textContent = `${((imageStats.total_size || 0) / (1024 * 1024 * 1024)).toFixed(2)}GB`;
                }

                // 加载图片列表
                const listResponse = await makeAuthenticatedRequest('/admin/images');
                if (listResponse.ok) {
                    const data = await listResponse.json();
                    allImages = data.images || [];
                    filteredImages = [...allImages];
                    renderImageTable();
                }
            } catch (error) {
                console.error('加载图片数据失败:', error);
                showImageMessage('加载图片数据失败', 'error');
            }
        }

        // 渲染图片表格
        function renderImageTable() {
            const tbody = document.getElementById('imagesTableBody');
            tbody.innerHTML = '';

            if (filteredImages.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: rgba(255,255,255,0.6);">暂无图片文件</td></tr>';
                return;
            }

            filteredImages.forEach(image => {
                const row = document.createElement('tr');
                
                const isSelected = selectedImages.has(image.filename);
                if (isSelected) {
                    row.style.background = 'rgba(102, 126, 234, 0.15)';
                }

                row.innerHTML = `
                    <td>
                        <input type="checkbox" 
                               ${isSelected ? 'checked' : ''} 
                               onchange="toggleImageSelection('${image.filename}')">
                    </td>
                    <td>
                        <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px; cursor: pointer; overflow: hidden;" 
                             onclick="viewImage('${image.filename}')" id="preview-${image.filename}">
                            📷
                        </div>
                    </td>
                    <td>
                        <div class="table-text" title="${image.filename}" style="max-width: 250px;">${image.filename}</div>
                    </td>
                    <td>
                        <span style="color: rgba(255,255,255,0.9); font-weight: 500;">${formatFileSize(image.size)}</span>
                    </td>
                    <td>
                        <span style="color: rgba(255,255,255,0.8); font-size: 13px;">${new Date(image.created_at).toLocaleString('zh-CN')}</span>
                    </td>
                    <td>
                        <div class="table-actions">
                            <button class="btn btn-secondary btn-sm" 
                                    onclick="downloadImage('${image.filename}')" 
                                    title="下载">📥</button>
                            <button class="btn btn-danger btn-sm" 
                                    onclick="deleteImage('${image.filename}')" 
                                    title="删除">🗑️</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            updateImageButtons();
            
            // 异步加载缩略图
            loadThumbnails();
        }

        // 异步加载缩略图
        async function loadThumbnails() {
            for (const image of filteredImages) {
                try {
                    const response = await makeAuthenticatedRequest(`/admin/images/${image.filename}?format=base64`);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success && data.base64_data) {
                            const previewDiv = document.getElementById(`preview-${image.filename}`);
                            if (previewDiv) {
                                previewDiv.innerHTML = `<img src="${data.base64_data}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 5px;">`;
                            }
                        }
                    }
                } catch (error) {
                    console.log(`加载缩略图失败 ${image.filename}:`, error);
                }
                // 添加小延迟避免请求过于频繁
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 切换图片选择
        function toggleImageSelection(filename) {
            if (selectedImages.has(filename)) {
                selectedImages.delete(filename);
            } else {
                selectedImages.add(filename);
            }
            
            // 只更新相关元素，不重新渲染整个表格
            updateSingleImageRow(filename);
            updateImageButtons();
        }

        // 更新单个图片行的状态
        function updateSingleImageRow(filename) {
            const tbody = document.getElementById('imagesTableBody');
            const rows = tbody.querySelectorAll('tr');
            
            rows.forEach(row => {
                const checkbox = row.querySelector('input[type="checkbox"]');
                if (checkbox && checkbox.getAttribute('onchange').includes(filename)) {
                    const isSelected = selectedImages.has(filename);
                    checkbox.checked = isSelected;
                    if (isSelected) {
                        row.style.background = 'rgba(102, 126, 234, 0.2)';
                    } else {
                        row.style.background = '';
                    }
                }
            });
        }

        // 全选/取消全选
        function toggleSelectAllImages() {
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            
            if (selectAllCheckbox.checked) {
                // 全选当前筛选的图片
                filteredImages.forEach(image => selectedImages.add(image.filename));
            } else {
                // 取消选择当前筛选的图片
                filteredImages.forEach(image => selectedImages.delete(image.filename));
            }
            
            // 批量更新所有行的状态，而不是重新渲染
            updateAllImageRows();
            updateImageButtons();
        }

        // 批量更新所有图片行的状态
        function updateAllImageRows() {
            const tbody = document.getElementById('imagesTableBody');
            const rows = tbody.querySelectorAll('tr');
            
            rows.forEach(row => {
                const checkbox = row.querySelector('input[type="checkbox"]');
                if (checkbox && checkbox.getAttribute('onchange')) {
                    // 从onchange属性中提取filename
                    const onchangeAttr = checkbox.getAttribute('onchange');
                    const filenameMatch = onchangeAttr.match(/toggleImageSelection\('([^']+)'\)/);
                    if (filenameMatch) {
                        const filename = filenameMatch[1];
                        const isSelected = selectedImages.has(filename);
                        checkbox.checked = isSelected;
                        if (isSelected) {
                            row.style.background = 'rgba(102, 126, 234, 0.2)';
                        } else {
                            row.style.background = '';
                        }
                    }
                }
            });
        }

        // 更新按钮状态
        function updateImageButtons() {
            const selectedCount = selectedImages.size;
            const batchDeleteBtn = document.getElementById('batchDeleteImagesBtn');
            const batchDownloadBtn = document.getElementById('batchDownloadBtn');
            const selectAllBtn = document.getElementById('selectAllImagesBtn');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');

            batchDeleteBtn.disabled = selectedCount === 0;
            batchDownloadBtn.disabled = selectedCount === 0;

            // 更新全选按钮状态
            const filteredFilenames = new Set(filteredImages.map(img => img.filename));
            const selectedFilteredCount = Array.from(selectedImages).filter(name => filteredFilenames.has(name)).length;
            
            if (selectedFilteredCount === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
                selectAllBtn.textContent = '☑️ 全选';
            } else if (selectedFilteredCount === filteredImages.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
                selectAllBtn.textContent = '☐ 取消全选';
            } else {
                selectAllCheckbox.indeterminate = true;
                selectAllBtn.textContent = `☑️ 全选 (${selectedFilteredCount}/${filteredImages.length})`;
            }
        }

        // 筛选图片
        function filterImages() {
            const searchTerm = document.getElementById('imageSearchInput').value.toLowerCase();
            const typeFilter = document.getElementById('imageTypeFilter').value;

            filteredImages = allImages.filter(image => {
                const matchesSearch = image.filename.toLowerCase().includes(searchTerm);
                const matchesType = !typeFilter || image.filename.toLowerCase().endsWith(typeFilter);
                return matchesSearch && matchesType;
            });

            // 重新渲染表格（筛选时需要重新渲染，但不重新加载缩略图）
            renderImageTableFast();
        }

        // 快速渲染表格（不重新加载缩略图）
        function renderImageTableFast() {
            const tbody = document.getElementById('imagesTableBody');
            tbody.innerHTML = '';

            if (filteredImages.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: rgba(255,255,255,0.6);">暂无图片文件</td></tr>';
                updateImageButtons();
                return;
            }

            // 先存储已加载的缩略图
            const thumbnailCache = new Map();
            document.querySelectorAll('[id^="preview-"]').forEach(div => {
                const img = div.querySelector('img');
                if (img) {
                    const filename = div.id.replace('preview-', '');
                    thumbnailCache.set(filename, img.src);
                }
            });

            filteredImages.forEach(image => {
                const row = document.createElement('tr');
                row.style.borderBottom = '1px solid rgba(255,255,255,0.1)';
                
                const isSelected = selectedImages.has(image.filename);
                if (isSelected) {
                    row.style.background = 'rgba(102, 126, 234, 0.2)';
                }

                row.innerHTML = `
                    <td>
                        <input type="checkbox" 
                               ${isSelected ? 'checked' : ''} 
                               onchange="toggleImageSelection('${image.filename}')">
                    </td>
                    <td>
                        <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 12px; cursor: pointer; overflow: hidden;" 
                             onclick="viewImage('${image.filename}')" id="preview-${image.filename}">
                            📷
                        </div>
                    </td>
                    <td>
                        <div class="table-text" title="${image.filename}" style="max-width: 250px;">${image.filename}</div>
                    </td>
                    <td>
                        <span style="color: rgba(255,255,255,0.9); font-weight: 500;">${formatFileSize(image.size)}</span>
                    </td>
                    <td>
                        <span style="color: rgba(255,255,255,0.8); font-size: 13px;">${new Date(image.created_at).toLocaleString('zh-CN')}</span>
                    </td>
                    <td>
                        <div class="table-actions">
                            <button class="btn btn-secondary btn-sm" 
                                    onclick="downloadImage('${image.filename}')" 
                                    title="下载">📥</button>
                            <button class="btn btn-danger btn-sm" 
                                    onclick="deleteImage('${image.filename}')" 
                                    title="删除">🗑️</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);

                // 恢复已缓存的缩略图
                if (thumbnailCache.has(image.filename)) {
                    const previewDiv = document.getElementById(`preview-${image.filename}`);
                    if (previewDiv) {
                        previewDiv.innerHTML = `<img src="${thumbnailCache.get(image.filename)}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 5px;">`;
                    }
                }
            });

            updateImageButtons();
        }

        // 刷新图片列表
        function refreshImageList() {
            selectedImages.clear();
            loadImages();
        }

        // 查看图片
        async function viewImage(filename) {
            try {
                // 获取图片的base64数据
                const response = await makeAuthenticatedRequest(`/admin/images/${filename}?format=base64`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.base64_data) {
                        // 在新窗口中显示图片
                        const newWindow = window.open('', '_blank');
                        newWindow.document.write(`
                            <!DOCTYPE html>
                            <html>
                            <head>
                                <title>${filename}</title>
                                <style>
                                    body {
                                        margin: 0;
                                        padding: 20px;
                                        background: #000;
                                        display: flex;
                                        justify-content: center;
                                        align-items: center;
                                        min-height: 100vh;
                                        font-family: Arial, sans-serif;
                                    }
                                    .container {
                                        text-align: center;
                                    }
                                    img {
                                        max-width: 90vw;
                                        max-height: 90vh;
                                        object-fit: contain;
                                        border-radius: 8px;
                                        box-shadow: 0 4px 20px rgba(255,255,255,0.1);
                                    }
                                    .filename {
                                        color: white;
                                        margin-top: 20px;
                                        font-size: 16px;
                                        word-break: break-all;
                                    }
                                    .info {
                                        color: rgba(255,255,255,0.7);
                                        margin-top: 10px;
                                        font-size: 14px;
                                    }
                                </style>
                            </head>
                            <body>
                                <div class="container">
                                    <img src="${data.base64_data}" alt="${filename}">
                                    <div class="filename">${filename}</div>
                                    <div class="info">类型: ${data.mime_type} | 大小: ${(data.data_length / 1024).toFixed(2)} KB</div>
                                </div>
                            </body>
                            </html>
                        `);
                    } else {
                        alert('无法加载图片预览');
                    }
                } else {
                    alert('获取图片失败');
                }
            } catch (error) {
                console.error('查看图片失败:', error);
                alert('查看图片失败: ' + error.message);
            }
        }

        // 下载图片
        function downloadImage(filename) {
            const link = document.createElement('a');
            link.href = `/admin/images/${filename}`;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 删除单个图片
        async function deleteImage(filename) {
            if (!confirm(`确定要删除图片 "${filename}" 吗？`)) return;

            try {
                const response = await makeAuthenticatedRequest(`/admin/images/${filename}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    showImageMessage(`图片 "${filename}" 删除成功`, 'success');
                    selectedImages.delete(filename);
                    loadImages();
                } else {
                    const error = await response.json();
                    showImageMessage(`删除失败: ${error.detail}`, 'error');
                }
            } catch (error) {
                showImageMessage(`删除失败: ${error.message}`, 'error');
            }
        }

        // 批量删除图片
        async function batchDeleteImages() {
            if (selectedImages.size === 0) return;

            if (!confirm(`确定要删除选中的 ${selectedImages.size} 个图片吗？此操作不可撤销！`)) return;

            try {
                const response = await makeAuthenticatedRequest('/admin/images/batch-delete', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(Array.from(selectedImages))
                });

                if (response.ok) {
                    const result = await response.json();
                    showImageMessage(`批量删除完成: 成功 ${result.success_count} 个，失败 ${result.failed_count} 个`, 'success');
                    selectedImages.clear();
                    loadImages();
                } else {
                    const error = await response.json();
                    showImageMessage(`批量删除失败: ${error.detail}`, 'error');
                }
            } catch (error) {
                showImageMessage(`批量删除失败: ${error.message}`, 'error');
            }
        }

        // 批量下载图片
        async function batchDownloadImages() {
            if (selectedImages.size === 0) return;

            for (const filename of selectedImages) {
                await new Promise(resolve => setTimeout(resolve, 100)); // 添加小延迟避免浏览器阻止
                downloadImage(filename);
            }

            showImageMessage(`开始下载 ${selectedImages.size} 个图片`, 'success');
        }

        // 显示图片消息
        function showImageMessage(message, type) {
            const messageDiv = document.getElementById('imageMessage');
            messageDiv.textContent = message;
            if (type === 'error') {
                messageDiv.className = 'error-message';
            } else if (type === 'warning') {
                messageDiv.className = 'warning-message';
            } else {
                messageDiv.className = 'success-message';
            }
            messageDiv.style.display = 'block';

            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }

        // ========== 角色管理相关函数 ==========
        let allCharacters = [];

        // 根据提示词ID获取显示名称
        function getPromptDisplayName(promptId) {
            if (!promptId) {
                return '<span style="color: rgba(255,255,255,0.5);">🚫 无系统提示词</span>';
            }
            
            if (window.promptsLookup && window.promptsLookup[promptId]) {
                return window.promptsLookup[promptId].name;
            }
            
            // 如果找不到对应的提示词名称，显示ID并标记
            return `<span style="color: rgba(255,255,255,0.7);" title="提示词ID: ${promptId}">${promptId}</span>`;
        }

        // 加载角色列表
        async function loadCharacters() {
            try {
                // 同时加载角色和提示词数据
                const [charactersResponse, promptsResponse] = await Promise.all([
                    makeAuthenticatedRequest('/admin/characters'),
                    makeAuthenticatedRequest('/admin/prompts')
                ]);
                
                if (charactersResponse.ok) {
                    const charactersData = await charactersResponse.json();
                    allCharacters = Object.values(charactersData.characters || {});
                }
                
                if (promptsResponse.ok) {
                    const promptsData = await promptsResponse.json();
                    // 将提示词数组转换为以ID为键的对象以便快速查找
                    const promptsObject = {};
                    Object.values(promptsData.prompts || {}).forEach(prompt => {
                        promptsObject[prompt.id] = prompt;
                    });
                    // 更新全局的allPrompts数组和对象
                    allPrompts = Object.values(promptsData.prompts || {});
                    window.promptsLookup = promptsObject; // 用于ID到名称的快速查找
                }
                
                renderCharacterTable();
            } catch (error) {
                console.error('加载角色列表失败:', error);
                showCharacterMessage('加载角色列表失败', 'error');
            }
        }

        // 渲染角色表格
        function renderCharacterTable() {
            const tbody = document.getElementById('charactersTableBody');
            tbody.innerHTML = '';

            if (allCharacters.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: rgba(255,255,255,0.6);">暂无AI角色</td></tr>';
                return;
            }

            allCharacters.forEach(character => {
                const row = document.createElement('tr');

                const statusBadge = character.is_active 
                    ? '<span class="status-badge status-active">● 激活</span>' 
                    : '<span class="status-badge status-inactive">● 禁用</span>';
                
                const defaultBadge = character.is_default 
                    ? '<span class="status-badge status-default">★ 默认</span>' 
                    : '';

                // 格式化计费信息显示
                const pricingInfo = formatPricingInfo(character.pricing);
                
                row.innerHTML = `
                    <td>
                        <div class="table-text" title="${character.name}" style="font-weight: 500;">${character.name}</div>
                    </td>
                    <td>
                        <div class="table-text" title="${character.display_name}">${character.display_name}</div>
                    </td>
                    <td>
                        <div class="table-text" title="${character.backend_model}">
                            <code style="background: rgba(255,255,255,0.1); padding: 2px 6px; border-radius: 4px; font-size: 12px;">${character.backend_model}</code>
                        </div>
                    </td>
                    <td>
                        <div class="table-text">${getPromptDisplayName(character.system_prompt_id)}</div>
                    </td>
                    <td>
                        <div style="font-size: 12px;">${pricingInfo}</div>
                    </td>
                    <td>
                        <div style="display: flex; flex-direction: column; gap: 4px;">
                            ${statusBadge}
                            ${defaultBadge}
                        </div>
                    </td>
                    <td>
                        <div class="table-actions">
                            <button class="btn btn-secondary btn-sm" 
                                    onclick="editCharacter('${character.id}')" 
                                    title="编辑">✏️</button>
                            <button class="btn btn-secondary btn-sm" 
                                    onclick="copyCharacter('${character.id}')" 
                                    title="复制">📋</button>
                            <button class="btn btn-warning btn-sm" 
                                    onclick="showCharacterPricingDetail('${character.id}')" 
                                    title="计费详情">💰</button>
                            <button class="btn btn-danger btn-sm" 
                                    onclick="deleteCharacter('${character.id}')" 
                                    title="删除">🗑️</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 刷新角色列表
        function refreshCharacterList() {
            loadCharacters();
        }

        // 显示创建角色对话框
        function showCreateCharacterDialog() {
            const characterData = {
                name: '',
                display_name: '',
                description: '',
                avatar_url: '',
                model_id: '',
                backend_model: '',
                system_prompt_id: '',
                category: '自定义',
                tags: [],
                is_active: true,
                is_default: false
            };
            
            showCharacterDialog(characterData, '创建新角色', 'create');
        }

        // 编辑角色
        async function editCharacter(characterId) {
            try {
                const response = await makeAuthenticatedRequest(`/admin/characters/${characterId}`);
                if (response.ok) {
                    const data = await response.json();
                    showCharacterDialog(data.character, '编辑角色', 'edit', characterId);
                }
            } catch (error) {
                showCharacterMessage('获取角色信息失败', 'error');
            }
        }

        // 显示角色对话框
        function showCharacterDialog(characterData, title, mode, characterId = null) {
            const dialogHtml = `
                <div class="dialog-overlay" id="characterDialog">
                    <div class="dialog-content" style="max-width: 700px;">
                        <h3 class="dialog-title">${title}</h3>
                        <div class="dialog-body">
                        <form id="characterForm">
                                <div class="form-row">
                                    <div class="form-group">
                                    <label class="form-label">角色名称 *</label>
                                    <input type="text" class="form-input" id="charName" value="${characterData.name}" required>
                                </div>
                                    <div class="form-group">
                                    <label class="form-label">显示名称 *</label>
                                    <input type="text" class="form-input" id="charDisplayName" value="${characterData.display_name}" required>
                                </div>
                            </div>
                                
                                <div class="form-row single">
                            <div class="form-group">
                                <label class="form-label">描述</label>
                                <textarea class="form-input" id="charDescription" rows="3">${characterData.description}</textarea>
                            </div>
                                </div>
                                
                                <div class="form-row single">
                            <div class="form-group">
                                <label class="form-label">头像URL</label>
                                <input type="url" class="form-input" id="charAvatarUrl" value="${characterData.avatar_url}">
                            </div>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                    <label class="form-label">模型ID *</label>
                                    <input type="text" class="form-input" id="charModelId" value="${characterData.model_id}" required>
                                </div>
                                    <div class="form-group">
                                    <label class="form-label">后端模型 *</label>
                                    <input type="text" class="form-input" id="charBackendModel" value="${characterData.backend_model}" required>
                                </div>
                            </div>
                                
                                <div class="form-row single">
                            <div class="form-group">
                                <label class="form-label">系统提示词</label>
                                <select class="form-input" id="charSystemPromptId">
                                    <option value="">🚫 无系统提示词（空白角色）</option>
                                </select>
                                <small style="color: rgba(255, 255, 255, 0.6); font-size: 12px; margin-top: 5px; display: block;">
                                    💡 选择"无系统提示词"将创建一个完全依靠用户输入的空白角色
                                </small>
                            </div>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                    <label class="form-label">分类</label>
                                    <input type="text" class="form-input" id="charCategory" value="${characterData.category}">
                                </div>
                                    <div class="form-group">
                                    <label class="form-label">标签 (逗号分隔)</label>
                                    <input type="text" class="form-input" id="charTags" value="${(characterData.tags || []).join(', ')}">
                                </div>
                            </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                    <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                        启用角色
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="charIsActive" ${characterData.is_active ? 'checked' : ''}>
                                            <span class="slider"></span>
                                        </label>
                                    </label>
                                </div>
                                    <div class="form-group">
                                    <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                        设为默认角色
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="charIsDefault" ${characterData.is_default ? 'checked' : ''}>
                                            <span class="slider"></span>
                                        </label>
                                    </label>
                                </div>
                            </div>
                            
                            <!-- 计费配置区域 -->
                                <div class="form-section">
                                    <div class="form-section-title">💰 计费配置</div>
                                
                                    <div class="form-group">
                                    <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                        启用独立计费
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="charEnablePricing" onchange="togglePricingConfig()">
                                            <span class="slider"></span>
                                        </label>
                                    </label>
                                </div>
                                
                                <div id="pricingConfigArea" style="display: none;">
                                        <div class="form-row single">
                                            <div class="form-group">
                                        <label class="form-label">计费模式</label>
                                        <select class="form-input" id="charPricingMode" onchange="switchPricingMode()">
                                            <option value="simple">简单计费</option>
                                            <option value="tiered">阶梯计费</option>
                                            <option value="staged">阶段计费</option>
                                        </select>
                                            </div>
                                    </div>
                                    
                                    <!-- 简单计费配置 -->
                                    <div id="simplePricingConfig">
                                            <h6 style="margin-bottom: 12px; color: rgba(255,255,255,0.9);">简单计费设置</h6>
                                            <div class="form-row">
                                                <div class="form-group">
                                                <label class="form-label">输入价格 ($/百万tokens)</label>
                                                <input type="number" class="form-input" id="simpleInputPrice" step="0.01" min="0" value="1.0">
                                            </div>
                                                <div class="form-group">
                                                <label class="form-label">输出价格 ($/百万tokens)</label>
                                                <input type="number" class="form-input" id="simpleOutputPrice" step="0.01" min="0" value="5.0">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 阶梯计费配置 -->
                                    <div id="tieredPricingConfig" style="display: none;">
                                            <h6 style="margin-bottom: 12px; color: rgba(255,255,255,0.9);">阶梯计费设置</h6>
                                            <div class="form-row">
                                                <div class="form-group">
                                                <label class="form-label">普通输入价格 ($/百万tokens)</label>
                                                <input type="number" class="form-input" id="tieredInputPrice" step="0.01" min="0" value="1.0">
                                            </div>
                                                <div class="form-group">
                                                <label class="form-label">普通输出价格 ($/百万tokens)</label>
                                                <input type="number" class="form-input" id="tieredOutputPrice" step="0.01" min="0" value="5.0">
                                            </div>
                                        </div>
                                            <div class="form-row">
                                                <div class="form-group">
                                                <label class="form-label">高价输入价格 ($/百万tokens)</label>
                                                <input type="number" class="form-input" id="tieredInputPriceHigh" step="0.01" min="0" value="2.0">
                                            </div>
                                                <div class="form-group">
                                                <label class="form-label">高价输出价格 ($/百万tokens)</label>
                                                <input type="number" class="form-input" id="tieredOutputPriceHigh" step="0.01" min="0" value="8.0">
                                            </div>
                                            </div>
                                            <div class="form-row single">
                                                <div class="form-group">
                                                <label class="form-label">阈值 (tokens)</label>
                                                <input type="number" class="form-input" id="tieredThreshold" step="1" min="0" value="100000">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 阶段计费配置 -->
                                    <div id="stagedPricingConfig" style="display: none;">
                                            <h6 style="margin-bottom: 12px; color: rgba(255,255,255,0.9);">阶段计费设置</h6>
                                            <div class="form-group">
                                            <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                                启用阶段计费
                                                <label class="toggle-switch">
                                                    <input type="checkbox" id="stagedEnable">
                                                    <span class="slider"></span>
                                                </label>
                                            </label>
                                        </div>
                                        <div id="stagesContainer">
                                            <!-- 阶段配置将动态生成 -->
                                        </div>
                                            <button type="button" class="btn btn-secondary btn-sm" onclick="addPricingStage()" style="margin-top: 12px;">➕ 添加阶段</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        </div>
                        <div class="dialog-footer">
                            <button type="button" class="btn btn-secondary" onclick="closeCharacterDialog()">取消</button>
                            <button type="submit" class="btn btn-primary" form="characterForm">${mode === 'create' ? '创建角色' : '保存更改'}</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', dialogHtml);
            
            // 加载提示词选项
            loadPromptOptions(characterData.system_prompt_id);
            
            // 初始化计费配置
            initializePricingConfig(characterData);
            
            // 绑定表单提交事件
            document.getElementById('characterForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                await saveCharacter(mode, characterId);
            });
        }

        // 加载提示词选项
        async function loadPromptOptions(selectedPromptId = '') {
            try {
                const response = await makeAuthenticatedRequest('/admin/prompts');
                if (response.ok) {
                    const data = await response.json();
                    const select = document.getElementById('charSystemPromptId');
                    
                    Object.values(data.prompts || {}).forEach(prompt => {
                        const option = document.createElement('option');
                        option.value = prompt.id;
                        option.textContent = prompt.name;
                        if (prompt.id === selectedPromptId) {
                            option.selected = true;
                        }
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载提示词选项失败:', error);
            }
        }

        // 保存角色
        async function saveCharacter(mode, characterId) {
            const formData = {
                name: document.getElementById('charName').value,
                display_name: document.getElementById('charDisplayName').value,
                description: document.getElementById('charDescription').value,
                avatar_url: document.getElementById('charAvatarUrl').value,
                model_id: document.getElementById('charModelId').value,
                backend_model: document.getElementById('charBackendModel').value,
                system_prompt_id: document.getElementById('charSystemPromptId').value,
                category: document.getElementById('charCategory').value,
                tags: document.getElementById('charTags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
                is_active: document.getElementById('charIsActive').checked,
                is_default: document.getElementById('charIsDefault').checked,
                // 添加计费配置
                pricing: collectPricingConfig()
            };

            try {
                const url = mode === 'create' ? '/admin/characters' : `/admin/characters/${characterId}`;
                const method = mode === 'create' ? 'POST' : 'PUT';
                
                const response = await makeAuthenticatedRequest(url, {
                    method: method,
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(formData)
                });

                if (response.ok) {
                    const result = await response.json();
                    showCharacterMessage(result.message, 'success');
                    closeCharacterDialog();
                    loadCharacters();
                } else {
                    const error = await response.json();
                    alert(`保存失败: ${error.detail}`);
                }
            } catch (error) {
                alert(`保存失败: ${error.message}`);
            }
        }

        // 关闭角色对话框
        function closeCharacterDialog() {
            const dialog = document.getElementById('characterDialog');
            if (dialog) {
                dialog.remove();
            }
        }

        // ========== 计费配置相关函数 ==========
        
        // 初始化计费配置
        function initializePricingConfig(characterData) {
            const pricing = characterData.pricing || {
                enable_independent_pricing: false,
                pricing_mode: 'simple',
                simple_pricing: { input_price_1m: 1.0, output_price_1m: 5.0 },
                tiered_pricing: { 
                    input_price_1m: 1.0, input_price_1m_high: 2.0, 
                    output_price_1m: 5.0, output_price_1m_high: 8.0, 
                    threshold_tokens: 100000 
                },
                staged_pricing: { enable_staged: false, stages: [] }
            };
            
            // 设置独立计费开关
            document.getElementById('charEnablePricing').checked = pricing.enable_independent_pricing;
            
            // 设置计费模式
            document.getElementById('charPricingMode').value = pricing.pricing_mode;
            
            // 初始化各种计费配置
            if (pricing.simple_pricing) {
                document.getElementById('simpleInputPrice').value = pricing.simple_pricing.input_price_1m || 1.0;
                document.getElementById('simpleOutputPrice').value = pricing.simple_pricing.output_price_1m || 5.0;
            }
            
            if (pricing.tiered_pricing) {
                document.getElementById('tieredInputPrice').value = pricing.tiered_pricing.input_price_1m || 1.0;
                document.getElementById('tieredOutputPrice').value = pricing.tiered_pricing.output_price_1m || 5.0;
                document.getElementById('tieredInputPriceHigh').value = pricing.tiered_pricing.input_price_1m_high || 2.0;
                document.getElementById('tieredOutputPriceHigh').value = pricing.tiered_pricing.output_price_1m_high || 8.0;
                document.getElementById('tieredThreshold').value = pricing.tiered_pricing.threshold_tokens || 100000;
            }
            
            if (pricing.staged_pricing) {
                document.getElementById('stagedEnable').checked = pricing.staged_pricing.enable_staged || false;
                renderPricingStages(pricing.staged_pricing.stages || []);
            }
            
            // 显示/隐藏配置区域
            togglePricingConfig();
            switchPricingMode();
        }
        
        // 切换计费配置显示
        function togglePricingConfig() {
            const enablePricing = document.getElementById('charEnablePricing').checked;
            const configArea = document.getElementById('pricingConfigArea');
            configArea.style.display = enablePricing ? 'block' : 'none';
        }
        
        // 切换计费模式
        function switchPricingMode() {
            const mode = document.getElementById('charPricingMode').value;
            
            // 隐藏所有配置区域
            document.getElementById('simplePricingConfig').style.display = 'none';
            document.getElementById('tieredPricingConfig').style.display = 'none';
            document.getElementById('stagedPricingConfig').style.display = 'none';
            
            // 显示对应的配置区域
            document.getElementById(mode + 'PricingConfig').style.display = 'block';
        }
        
        // 添加阶段计费阶段
        function addPricingStage() {
            const container = document.getElementById('stagesContainer');
            const stageIndex = container.children.length;
            
            const stageHtml = `
                <div class="pricing-stage" style="border: 1px solid rgba(255,255,255,0.1); border-radius: 8px; padding: 15px; margin-bottom: 10px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <h6>阶段 ${stageIndex + 1}</h6>
                        <button type="button" class="btn btn-danger" style="padding: 2px 6px; font-size: 12px;" onclick="removePricingStage(this)">删除</button>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 10px;">
                        <div>
                            <label class="form-label">阶段名称</label>
                            <input type="text" class="form-input stage-name" value="阶段${stageIndex + 1}" style="font-size: 12px; padding: 5px;">
                        </div>
                        <div>
                            <label class="form-label">最大消息数 (-1为无限)</label>
                            <input type="number" class="form-input stage-max-messages" value="${stageIndex === 0 ? 50 : -1}" style="font-size: 12px; padding: 5px;">
                        </div>
                        <div>
                            <label class="form-label">输入价格</label>
                            <input type="number" class="form-input stage-input-price" step="0.01" min="0" value="1.0" style="font-size: 12px; padding: 5px;">
                        </div>
                        <div>
                            <label class="form-label">输出价格</label>
                            <input type="number" class="form-input stage-output-price" step="0.01" min="0" value="5.0" style="font-size: 12px; padding: 5px;">
                        </div>
                    </div>
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', stageHtml);
        }
        
        // 删除阶段计费阶段
        function removePricingStage(button) {
            const stage = button.closest('.pricing-stage');
            stage.remove();
            
            // 重新编号剩余阶段
            const stages = document.querySelectorAll('.pricing-stage');
            stages.forEach((stage, index) => {
                stage.querySelector('h6').textContent = `阶段 ${index + 1}`;
            });
        }
        
        // 渲染阶段计费配置
        function renderPricingStages(stages) {
            const container = document.getElementById('stagesContainer');
            container.innerHTML = '';
            
            stages.forEach((stage, index) => {
                const stageHtml = `
                    <div class="pricing-stage" style="border: 1px solid rgba(255,255,255,0.1); border-radius: 8px; padding: 15px; margin-bottom: 10px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h6>阶段 ${index + 1}</h6>
                            <button type="button" class="btn btn-danger" style="padding: 2px 6px; font-size: 12px;" onclick="removePricingStage(this)">删除</button>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 10px;">
                            <div>
                                <label class="form-label">阶段名称</label>
                                <input type="text" class="form-input stage-name" value="${stage.stage_name || ''}" style="font-size: 12px; padding: 5px;">
                            </div>
                            <div>
                                <label class="form-label">最大消息数 (-1为无限)</label>
                                <input type="number" class="form-input stage-max-messages" value="${stage.max_messages || -1}" style="font-size: 12px; padding: 5px;">
                            </div>
                            <div>
                                <label class="form-label">输入价格</label>
                                <input type="number" class="form-input stage-input-price" step="0.01" min="0" value="${stage.input_price_1m || 1.0}" style="font-size: 12px; padding: 5px;">
                            </div>
                            <div>
                                <label class="form-label">输出价格</label>
                                <input type="number" class="form-input stage-output-price" step="0.01" min="0" value="${stage.output_price_1m || 5.0}" style="font-size: 12px; padding: 5px;">
                            </div>
                        </div>
                    </div>
                `;
                container.insertAdjacentHTML('beforeend', stageHtml);
            });
            
            // 如果没有阶段，添加一个默认阶段
            if (stages.length === 0) {
                addPricingStage();
            }
        }
        
        // 收集计费配置数据
        function collectPricingConfig() {
            const pricing = {
                enable_independent_pricing: document.getElementById('charEnablePricing').checked,
                pricing_mode: document.getElementById('charPricingMode').value,
                simple_pricing: {
                    input_price_1m: isNaN(parseFloat(document.getElementById('simpleInputPrice').value)) ? 1.0 : parseFloat(document.getElementById('simpleInputPrice').value),
                    output_price_1m: isNaN(parseFloat(document.getElementById('simpleOutputPrice').value)) ? 5.0 : parseFloat(document.getElementById('simpleOutputPrice').value)
                },
                tiered_pricing: {
                    input_price_1m: isNaN(parseFloat(document.getElementById('tieredInputPrice').value)) ? 1.0 : parseFloat(document.getElementById('tieredInputPrice').value),
                    input_price_1m_high: isNaN(parseFloat(document.getElementById('tieredInputPriceHigh').value)) ? 2.0 : parseFloat(document.getElementById('tieredInputPriceHigh').value),
                    output_price_1m: isNaN(parseFloat(document.getElementById('tieredOutputPrice').value)) ? 5.0 : parseFloat(document.getElementById('tieredOutputPrice').value),
                    output_price_1m_high: isNaN(parseFloat(document.getElementById('tieredOutputPriceHigh').value)) ? 8.0 : parseFloat(document.getElementById('tieredOutputPriceHigh').value),
                    threshold_tokens: isNaN(parseInt(document.getElementById('tieredThreshold').value)) ? 100000 : parseInt(document.getElementById('tieredThreshold').value)
                },
                staged_pricing: {
                    enable_staged: document.getElementById('stagedEnable').checked,
                    stages: []
                }
            };
            
            // 收集阶段计费数据
            const stages = document.querySelectorAll('.pricing-stage');
            stages.forEach(stage => {
                const stageData = {
                    stage_name: stage.querySelector('.stage-name').value,
                    max_messages: parseInt(stage.querySelector('.stage-max-messages').value) || -1,
                    input_price_1m: isNaN(parseFloat(stage.querySelector('.stage-input-price').value)) ? 1.0 : parseFloat(stage.querySelector('.stage-input-price').value),
                    output_price_1m: isNaN(parseFloat(stage.querySelector('.stage-output-price').value)) ? 5.0 : parseFloat(stage.querySelector('.stage-output-price').value)
                };
                pricing.staged_pricing.stages.push(stageData);
            });
            
                         return pricing;
         }

        // 格式化计费信息显示
        function formatPricingInfo(pricing) {
            if (!pricing || !pricing.enable_independent_pricing) {
                return '<span style="color: #6c757d;">使用模型默认</span>';
            }
            
            const mode = pricing.pricing_mode || 'simple';
            let modeText = '';
            let priceText = '';
            
            switch (mode) {
                case 'simple':
                    modeText = '简单计费';
                    const simple = pricing.simple_pricing || {};
                    priceText = `输入:$${simple.input_price_1m || 0}/M 输出:$${simple.output_price_1m || 0}/M`;
                    break;
                case 'tiered':
                    modeText = '阶梯计费';
                    const tiered = pricing.tiered_pricing || {};
                    priceText = `$${tiered.input_price_1m || 0}→$${tiered.input_price_1m_high || 0}/M (${tiered.threshold_tokens || 0})`;
                    break;
                case 'staged':
                    modeText = '阶段计费';
                    const staged = pricing.staged_pricing || {};
                    const stageCount = staged.stages ? staged.stages.length : 0;
                    priceText = `${stageCount}个阶段`;
                    break;
                default:
                    modeText = '未知模式';
                    priceText = '';
            }
            
                    return `
            <div style="font-size: 12px;">
                <div style="color: #28a745; font-weight: bold;">${modeText}</div>
                <div style="color: #ffffff;">${priceText}</div>
            </div>
        `;
        }

        // 显示角色计费详情
        async function showCharacterPricingDetail(characterId) {
            try {
                const response = await makeAuthenticatedRequest(`/admin/characters/${characterId}/pricing`);
                if (response.ok) {
                    const data = await response.json();
                    const character = allCharacters.find(c => c.id === characterId);
                    
                    const dialogHtml = `
                        <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.8); z-index: 10000; display: flex; align-items: center; justify-content: center;" id="pricingDetailDialog">
                            <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(25px); border-radius: 20px; padding: 30px; width: 90%; max-width: 700px; max-height: 80vh; overflow-y: auto;">
                                <h3 style="margin-bottom: 20px;">💰 ${character.name} - 计费配置详情</h3>
                                ${renderPricingDetailInfo(data.pricing)}
                                <div style="text-align: center; margin-top: 30px;">
                                    <button type="button" class="btn btn-secondary" onclick="closePricingDetailDialog()" style="margin-right: 10px;">关闭</button>
                                    <button type="button" class="btn btn-primary" onclick="editCharacterFromPricing('${characterId}')">编辑角色</button>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    document.body.insertAdjacentHTML('beforeend', dialogHtml);
                } else {
                    const error = await response.json();
                    alert(`获取计费详情失败: ${error.detail}`);
                }
            } catch (error) {
                alert(`获取计费详情失败: ${error.message}`);
            }
        }

        // 渲染计费详情信息
        function renderPricingDetailInfo(pricing) {
            if (!pricing || !pricing.enable_independent_pricing) {
                return `
                    <div style="text-align: center; padding: 2rem; color: #6c757d;">
                        <h4>该角色使用模型默认计费</h4>
                        <p>未启用独立计费配置</p>
                    </div>
                `;
            }
            
            const mode = pricing.pricing_mode || 'simple';
            let detailHtml = `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #28a745;">✅ 已启用独立计费</h4>
                    <p style="color: #6c757d;">计费模式: <strong>${getPricingModeText(mode)}</strong></p>
                </div>
            `;
            
            switch (mode) {
                case 'simple':
                    const simple = pricing.simple_pricing || {};
                    detailHtml += `
                        <div style="border: 1px solid rgba(255,255,255,0.2); border-radius: 10px; padding: 15px;">
                            <h5>简单计费设置</h5>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 10px;">
                                <div>
                                    <strong>输入价格:</strong> $${simple.input_price_1m || 0}/百万tokens
                                </div>
                                <div>
                                    <strong>输出价格:</strong> $${simple.output_price_1m || 0}/百万tokens
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'tiered':
                    const tiered = pricing.tiered_pricing || {};
                    detailHtml += `
                        <div style="border: 1px solid rgba(255,255,255,0.2); border-radius: 10px; padding: 15px;">
                            <h5>阶梯计费设置</h5>
                            <div style="margin-top: 10px;">
                                <div style="margin-bottom: 10px;">
                                    <strong>普通价格 (≤${tiered.threshold_tokens || 0} tokens):</strong>
                                    <br>输入: $${tiered.input_price_1m || 0}/M, 输出: $${tiered.output_price_1m || 0}/M
                                </div>
                                <div>
                                    <strong>高价阶梯 (>${tiered.threshold_tokens || 0} tokens):</strong>
                                    <br>输入: $${tiered.input_price_1m_high || 0}/M, 输出: $${tiered.output_price_1m_high || 0}/M
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'staged':
                    const staged = pricing.staged_pricing || {};
                    detailHtml += `
                        <div style="border: 1px solid rgba(255,255,255,0.2); border-radius: 10px; padding: 15px;">
                            <h5>阶段计费设置</h5>
                            <div style="margin: 10px 0;">
                                <strong>阶段计费状态:</strong> ${staged.enable_staged ? '<span style="color: #28a745;">启用</span>' : '<span style="color: #dc3545;">禁用</span>'}
                            </div>
                            ${staged.stages && staged.stages.length > 0 ? `
                                <div style="margin-top: 15px;">
                                    <h6>阶段配置:</h6>
                                    ${staged.stages.map((stage, index) => `
                                        <div style="background: rgba(255,255,255,0.05); border-radius: 8px; padding: 10px; margin: 5px 0;">
                                            <strong>阶段 ${index + 1}: ${stage.stage_name}</strong>
                                            <br>消息数限制: ${stage.max_messages === -1 ? '无限' : stage.max_messages}
                                            <br>输入: $${stage.input_price_1m}/M, 输出: $${stage.output_price_1m}/M
                                        </div>
                                    `).join('')}
                                </div>
                            ` : '<p style="color: #6c757d;">未配置阶段</p>'}
                        </div>
                    `;
                    break;
            }
            
            return detailHtml;
        }

        // 获取计费模式文本
        function getPricingModeText(mode) {
            const modeMap = {
                'simple': '简单计费',
                'tiered': '阶梯计费', 
                'staged': '阶段计费'
            };
            return modeMap[mode] || '未知模式';
        }

        // 关闭计费详情对话框
        function closePricingDetailDialog() {
            const dialog = document.getElementById('pricingDetailDialog');
            if (dialog) {
                dialog.remove();
            }
        }

                 // 从计费详情编辑角色
         function editCharacterFromPricing(characterId) {
             closePricingDetailDialog();
             editCharacter(characterId);
         }

         // 显示角色计费摘要
         async function showCharactersPricingSummary() {
             try {
                 const response = await makeAuthenticatedRequest('/admin/characters/pricing/summary');
                 if (response.ok) {
                     const data = await response.json();
                     
                     const dialogHtml = `
                         <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.8); z-index: 10000; display: flex; align-items: center; justify-content: center;" id="pricingSummaryDialog">
                             <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(25px); border-radius: 20px; padding: 30px; width: 95%; max-width: 1000px; max-height: 80vh; overflow-y: auto;">
                                 <h3 style="margin-bottom: 20px;">💰 所有角色计费配置摘要</h3>
                                 <div style="max-height: 500px; overflow-y: auto;">
                                     <table style="width: 100%; border-collapse: collapse;">
                                         <thead style="background: rgba(255,255,255,0.1); position: sticky; top: 0;">
                                             <tr>
                                                 <th style="padding: 10px; text-align: left; border-bottom: 1px solid rgba(255,255,255,0.2);">角色名称</th>
                                                 <th style="padding: 10px; text-align: left; border-bottom: 1px solid rgba(255,255,255,0.2);">独立计费</th>
                                                 <th style="padding: 10px; text-align: left; border-bottom: 1px solid rgba(255,255,255,0.2);">计费模式</th>
                                                 <th style="padding: 10px; text-align: left; border-bottom: 1px solid rgba(255,255,255,0.2);">输入价格</th>
                                                 <th style="padding: 10px; text-align: left; border-bottom: 1px solid rgba(255,255,255,0.2);">输出价格</th>
                                                 <th style="padding: 10px; text-align: left; border-bottom: 1px solid rgba(255,255,255,0.2);">描述</th>
                                             </tr>
                                         </thead>
                                         <tbody>
                                             ${data.summary.map(item => `
                                                 <tr style="border-bottom: 1px solid rgba(255,255,255,0.1);">
                                                     <td style="padding: 10px;">${item.character_name}</td>
                                                     <td style="padding: 10px;">
                                                         ${item.independent_pricing 
                                                             ? '<span style="color: #28a745;">✅ 启用</span>' 
                                                             : '<span style="color: #6c757d;">❌ 禁用</span>'}
                                                     </td>
                                                     <td style="padding: 10px;">${getPricingModeText(item.pricing_mode)}</td>
                                                     <td style="padding: 10px;">$${item.price_info.input_price}/M</td>
                                                     <td style="padding: 10px;">$${item.price_info.output_price}/M</td>
                                                     <td style="padding: 10px; font-size: 12px; color: #6c757d;">${item.price_info.description}</td>
                                                 </tr>
                                             `).join('')}
                                         </tbody>
                                     </table>
                                 </div>
                                 <div style="text-align: center; margin-top: 30px;">
                                     <button type="button" class="btn btn-secondary" onclick="closePricingSummaryDialog()">关闭</button>
                                 </div>
                             </div>
                         </div>
                     `;
                     
                     document.body.insertAdjacentHTML('beforeend', dialogHtml);
                 } else {
                     const error = await response.json();
                     alert(`获取计费摘要失败: ${error.detail}`);
                 }
             } catch (error) {
                 alert(`获取计费摘要失败: ${error.message}`);
             }
         }

         // 关闭计费摘要对话框
         function closePricingSummaryDialog() {
             const dialog = document.getElementById('pricingSummaryDialog');
             if (dialog) {
                 dialog.remove();
             }
         }

         // 复制角色
        async function copyCharacter(characterId) {
            const newName = prompt('请输入新角色的名称:');
            if (!newName) return;

            try {
                const response = await makeAuthenticatedRequest(`/admin/characters/${characterId}/copy`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({new_name: newName})
                });

                if (response.ok) {
                    const result = await response.json();
                    showCharacterMessage(result.message, 'success');
                    loadCharacters();
                } else {
                    const error = await response.json();
                    showCharacterMessage(`复制失败: ${error.detail}`, 'error');
                }
            } catch (error) {
                showCharacterMessage(`复制失败: ${error.message}`, 'error');
            }
        }

        // 删除角色
        async function deleteCharacter(characterId) {
            const character = allCharacters.find(c => c.id === characterId);
            if (!character) return;

            if (!confirm(`确定要删除角色 "${character.name}" 吗？`)) return;

            try {
                const response = await makeAuthenticatedRequest(`/admin/characters/${characterId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    const result = await response.json();
                    showCharacterMessage(result.message, 'success');
                    loadCharacters();
                } else {
                    const error = await response.json();
                    showCharacterMessage(`删除失败: ${error.detail}`, 'error');
                }
            } catch (error) {
                showCharacterMessage(`删除失败: ${error.message}`, 'error');
            }
        }

        // 显示角色消息
        function showCharacterMessage(message, type) {
            const messageDiv = document.getElementById('characterMessage');
            messageDiv.textContent = message;
            if (type === 'error') {
                messageDiv.className = 'error-message';
            } else if (type === 'warning') {
                messageDiv.className = 'warning-message';
            } else {
                messageDiv.className = 'success-message';
            }
            messageDiv.style.display = 'block';

            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }

        // 导出所有角色
        async function exportAllCharacters() {
            try {
                const response = await makeAuthenticatedRequest('/admin/characters');
                if (response.ok) {
                    const result = await response.json();
                    const exportData = {
                        format_version: '1.0',
                        export_time: new Date().toISOString(),
                        characters: result.characters
                    };
                    const dataStr = JSON.stringify(exportData, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(dataBlob);
                    link.download = `characters_export_${new Date().toISOString().slice(0,10)}.json`;
                    link.click();
                    
                    showCharacterMessage('角色导出成功', 'success');
                }
            } catch (error) {
                showCharacterMessage('导出失败: ' + error.message, 'error');
            }
        }

        // 显示导入角色对话框
        function showImportCharacterDialog() {
            const dialogHtml = `
                <div class="dialog-overlay" id="importCharacterDialog">
                    <div class="dialog-content">
                        <div class="dialog-title">📥 导入角色</div>
                        <div class="dialog-body">
                            <div class="form-group">
                                <label class="form-label">选择JSON文件</label>
                                <input type="file" class="form-input" id="importCharacterFile" accept=".json">
                                <div style="margin-top: 8px; font-size: 12px; color: rgba(255,255,255,0.6);">
                                    支持导出的角色文件、角色数组或单个角色对象格式
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                    覆盖现有角色
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="importCharacterOverwrite">
                                        <span class="slider"></span>
                                    </label>
                                </label>
                                <div style="margin-top: 8px; font-size: 12px; color: rgba(255,255,255,0.6);">
                                    开启后将更新已存在的同ID角色，否则跳过
                                </div>
                            </div>
                        </div>
                        <div class="dialog-footer">
                            <button type="button" class="btn btn-secondary" onclick="closeImportCharacterDialog()">取消</button>
                            <button type="button" class="btn btn-primary" onclick="importCharactersFromFile()">开始导入</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', dialogHtml);
        }

        // 关闭导入对话框
        function closeImportCharacterDialog() {
            const dialog = document.getElementById('importCharacterDialog');
            if (dialog) {
                dialog.remove();
            }
        }

        // 从文件导入角色
        async function importCharactersFromFile() {
            const fileInput = document.getElementById('importCharacterFile');
            const overwrite = document.getElementById('importCharacterOverwrite').checked;
            
            if (!fileInput.files[0]) {
                alert('请选择文件');
                return;
            }

            try {
                const fileContent = await fileInput.files[0].text();
                let importData;
                
                try {
                    importData = JSON.parse(fileContent);
                } catch (parseError) {
                    throw new Error('JSON文件格式错误，请检查文件格式');
                }
                
                // 验证文件格式并提取角色数据
                let charactersToImport = [];
                
                console.log('原始导入数据:', importData);
                console.log('importData.characters类型:', typeof importData.characters);
                console.log('importData.characters是否为数组:', Array.isArray(importData.characters));
                
                if (importData.characters) {
                    // 标准导出格式: {format_version: "1.0", characters: {id1: char1, id2: char2, ...}}
                    if (typeof importData.characters === 'object' && !Array.isArray(importData.characters)) {
                        // 对象格式，转换为数组
                        charactersToImport = Object.values(importData.characters);
                        console.log('检测到对象格式的角色数据，转换为数组:', charactersToImport.length, '个角色');
                    } else if (Array.isArray(importData.characters)) {
                        // 数组格式
                        charactersToImport = importData.characters;
                        console.log('检测到数组格式的角色数据:', charactersToImport.length, '个角色');
                    } else {
                        console.error('角色数据格式错误:', importData.characters);
                        throw new Error('导出文件中的角色数据格式不正确');
                    }
                } else if (Array.isArray(importData)) {
                    // 直接是角色数组格式
                    charactersToImport = importData;
                    console.log('检测到直接数组格式:', charactersToImport.length, '个角色');
                } else if (typeof importData === 'object' && importData.name && importData.display_name) {
                    // 单个角色对象格式
                    charactersToImport = [importData];
                    console.log('检测到单个角色对象格式');
                } else {
                    console.error('不支持的文件格式:', importData);
                    console.error('文件结构分析:');
                    console.error('- 是否有characters字段:', 'characters' in importData);
                    console.error('- importData是否为数组:', Array.isArray(importData));
                    console.error('- importData是否为对象:', typeof importData === 'object');
                    console.error('- importData是否有name字段:', 'name' in importData);
                    console.error('- importData是否有display_name字段:', 'display_name' in importData);
                    throw new Error('不支持的文件格式。支持的格式：\n1. 角色导出文件（包含characters对象）\n2. 角色数组\n3. 单个角色对象');
                }

                if (charactersToImport.length === 0) {
                    throw new Error('文件中没有找到有效的角色数据');
                }

                console.log('准备导入的角色数据:', charactersToImport);

                // 调用导入API
                const response = await makeAuthenticatedRequest('/admin/characters/import', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        characters: charactersToImport,
                        overwrite: overwrite
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    const results = result.results;
                    
                    let message = `导入完成！`;
                    let messageType = 'success';
                    
                    // 构建详细的结果信息
                    const summaryParts = [];
                    if (results.created > 0) summaryParts.push(`创建 ${results.created} 个角色`);
                    if (results.updated > 0) summaryParts.push(`更新 ${results.updated} 个角色`);
                    if (results.skipped > 0) summaryParts.push(`跳过 ${results.skipped} 个角色`);
                    
                    if (summaryParts.length > 0) {
                        message += '\n' + summaryParts.join('，');
                    }
                    
                    // 显示错误和警告信息
                    if (results.errors && results.errors.length > 0) {
                        message += '\n\n注意事项：';
                        results.errors.slice(0, 8).forEach((error, index) => {
                            message += `\n${index + 1}. ${error}`;
                        });
                        if (results.errors.length > 8) {
                            message += `\n... 还有 ${results.errors.length - 8} 个提醒`;
                        }
                        
                        // 如果有严重错误，改变消息类型
                        const hasSystemPromptErrors = results.errors.some(error => 
                            error.includes('提示词') && error.includes('不存在')
                        );
                        if (hasSystemPromptErrors || results.created === 0) {
                            messageType = 'warning';
                        }
                    }
                    
                    showCharacterMessage(message, messageType);
                    closeImportCharacterDialog();
                    refreshCharacterList(); // 刷新角色列表
                } else {
                    const error = await response.json();
                    throw new Error(error.detail || '导入失败');
                }
                
            } catch (error) {
                console.error('角色导入错误:', error);
                showCharacterMessage(`导入失败：${error.message}`, 'error');
            }
        }

        // ========== 提示词管理相关函数 ==========
        let allPrompts = [];

        // 加载提示词列表
        async function loadPrompts() {
            try {
                const response = await makeAuthenticatedRequest('/admin/prompts');
                if (response.ok) {
                    const data = await response.json();
                    allPrompts = Object.values(data.prompts || {});
                    renderPromptTable();
                }
            } catch (error) {
                console.error('加载提示词列表失败:', error);
                showPromptMessage('加载提示词列表失败', 'error');
            }
        }

        // 渲染提示词表格
        function renderPromptTable() {
            const tbody = document.getElementById('promptsTableBody');
            tbody.innerHTML = '';

            if (allPrompts.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: rgba(255,255,255,0.6);">暂无提示词</td></tr>';
                return;
            }

            allPrompts.forEach(prompt => {
                const row = document.createElement('tr');

                const tagsHtml = (prompt.tags || []).map(tag => 
                    `<span class="tag">${tag}</span>`
                ).join('');

                const systemBadge = prompt.is_system 
                    ? '<span class="status-badge status-system">🔒 系统</span>' 
                    : '';

                row.innerHTML = `
                    <td>
                        <div class="table-text" title="${prompt.name}" style="font-weight: 500;">
                            ${prompt.name}
                            ${systemBadge}
                        </div>
                    </td>
                    <td>
                        <div class="table-text multiline" title="${prompt.description || '无描述'}" style="max-width: 250px;">
                            ${prompt.description || '<span style="color: rgba(255,255,255,0.5);">无描述</span>'}
                        </div>
                    </td>
                    <td>
                        <div class="table-text">
                            <span class="tag">${prompt.category || '未分类'}</span>
                        </div>
                    </td>
                    <td>
                        <div class="tag-group">
                            ${tagsHtml || '<span style="color: rgba(255,255,255,0.5); font-size: 12px;">无标签</span>'}
                        </div>
                    </td>
                    <td>
                        <div class="table-text" style="font-size: 13px; color: rgba(255,255,255,0.8);">
                            ${prompt.author || '未知'}
                        </div>
                    </td>
                    <td>
                        <div class="table-actions">
                            <button class="btn btn-secondary btn-sm" 
                                    onclick="viewPrompt('${prompt.id}')" 
                                    title="查看">👁️</button>
                        ${!prompt.is_system ? `
                                <button class="btn btn-secondary btn-sm" 
                                        onclick="editPrompt('${prompt.id}')" 
                                        title="编辑">✏️</button>
                                <button class="btn btn-danger btn-sm" 
                                        onclick="deletePrompt('${prompt.id}')" 
                                        title="删除">🗑️</button>
                        ` : ''}
                            <button class="btn btn-secondary btn-sm" 
                                    onclick="copyPrompt('${prompt.id}')" 
                                    title="复制">📋</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 刷新提示词列表
        function refreshPromptList() {
            loadPrompts();
        }

        // 显示创建提示词对话框
        function showCreatePromptDialog() {
            const promptData = {
                name: '',
                description: '',
                content: '',
                category: '自定义',
                tags: [],
                version: '1.0.0',
                author: '用户'
            };
            
            showPromptDialog(promptData, '创建新提示词', 'create');
        }

        // 显示提示词对话框
        function showPromptDialog(promptData, title, mode, promptId = null) {
            const dialogHtml = `
                <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.8); z-index: 10000; display: flex; align-items: center; justify-content: center;" id="promptDialog">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(25px); border-radius: 20px; padding: 30px; width: 90%; max-width: 700px; max-height: 90vh; overflow-y: auto;">
                        <h3 style="margin-bottom: 20px;">${title}</h3>
                        <form id="promptForm">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div>
                                    <label class="form-label">提示词名称 *</label>
                                    <input type="text" class="form-input" id="promptName" value="${promptData.name}" required>
                                </div>
                                <div>
                                    <label class="form-label">分类</label>
                                    <input type="text" class="form-input" id="promptCategory" value="${promptData.category}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">描述</label>
                                <textarea class="form-input" id="promptDescription" rows="2">${promptData.description}</textarea>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div>
                                    <label class="form-label">标签 (逗号分隔)</label>
                                    <input type="text" class="form-input" id="promptTags" value="${(promptData.tags || []).join(', ')}">
                                </div>
                                <div>
                                    <label class="form-label">版本</label>
                                    <input type="text" class="form-input" id="promptVersion" value="${promptData.version || '1.0.0'}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">提示词内容</label>
                                <textarea class="form-input" id="promptContent" rows="8" placeholder="系统提示词内容（可为空，表示无特殊系统指令）">${promptData.content}</textarea>
                                <small style="color: rgba(255, 255, 255, 0.6); font-size: 12px; margin-top: 5px; display: block;">
                                    💡 提示：留空表示该角色不使用任何系统提示词，完全依靠用户输入进行对话
                                </small>
                            </div>
                            <div style="text-align: center; margin-top: 30px;">
                                <button type="button" class="btn btn-secondary" onclick="closePromptDialog()" style="margin-right: 10px;">取消</button>
                                <button type="submit" class="btn btn-primary">${mode === 'create' ? '创建' : '保存'}</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', dialogHtml);
            
            // 绑定表单提交事件
            document.getElementById('promptForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                await savePrompt(mode, promptId);
            });
        }

        // 保存提示词
        async function savePrompt(mode, promptId) {
            const formData = {
                name: document.getElementById('promptName').value,
                description: document.getElementById('promptDescription').value,
                content: document.getElementById('promptContent').value,
                category: document.getElementById('promptCategory').value,
                tags: document.getElementById('promptTags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
                version: document.getElementById('promptVersion').value,
                author: '用户'
            };

            try {
                const url = mode === 'create' ? '/admin/prompts' : `/admin/prompts/${promptId}`;
                const method = mode === 'create' ? 'POST' : 'PUT';
                
                const response = await makeAuthenticatedRequest(url, {
                    method: method,
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(formData)
                });

                if (response.ok) {
                    const result = await response.json();
                    showPromptMessage(result.message, 'success');
                    closePromptDialog();
                    loadPrompts();
                } else {
                    const error = await response.json();
                    alert(`保存失败: ${error.detail}`);
                }
            } catch (error) {
                alert(`保存失败: ${error.message}`);
            }
        }

        // 关闭提示词对话框
        function closePromptDialog() {
            const dialog = document.getElementById('promptDialog');
            if (dialog) {
                dialog.remove();
            }
        }

        // 编辑提示词
        async function editPrompt(promptId) {
            try {
                const response = await makeAuthenticatedRequest(`/admin/prompts/${promptId}`);
                if (response.ok) {
                    const data = await response.json();
                    showPromptDialog(data.prompt, '编辑提示词', 'edit', promptId);
                }
            } catch (error) {
                showPromptMessage('获取提示词信息失败', 'error');
            }
        }

        // 删除提示词
        async function deletePrompt(promptId) {
            const prompt = allPrompts.find(p => p.id === promptId);
            if (!prompt) return;

            if (!confirm(`确定要删除提示词 "${prompt.name}" 吗？`)) return;

            try {
                const response = await makeAuthenticatedRequest(`/admin/prompts/${promptId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    const result = await response.json();
                    showPromptMessage(result.message, 'success');
                    loadPrompts();
                } else {
                    const error = await response.json();
                    showPromptMessage(`删除失败: ${error.detail}`, 'error');
                }
            } catch (error) {
                showPromptMessage(`删除失败: ${error.message}`, 'error');
            }
        }

        // 复制提示词
        async function copyPrompt(promptId) {
            const newName = prompt('请输入新提示词的名称:');
            if (!newName) return;

            try {
                const response = await makeAuthenticatedRequest(`/admin/prompts/${promptId}/copy`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({new_name: newName})
                });

                if (response.ok) {
                    const result = await response.json();
                    showPromptMessage(result.message, 'success');
                    loadPrompts();
                } else {
                    const error = await response.json();
                    showPromptMessage(`复制失败: ${error.detail}`, 'error');
                }
            } catch (error) {
                showPromptMessage(`复制失败: ${error.message}`, 'error');
            }
        }

        // 导出所有提示词
        async function exportAllPrompts() {
            try {
                const response = await makeAuthenticatedRequest('/admin/prompts/export/all');
                if (response.ok) {
                    const result = await response.json();
                    const dataStr = JSON.stringify(result.export_data, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(dataBlob);
                    link.download = `prompts_export_${new Date().toISOString().slice(0,10)}.json`;
                    link.click();
                    
                    showPromptMessage('提示词导出成功', 'success');
                }
            } catch (error) {
                showPromptMessage('导出失败: ' + error.message, 'error');
            }
        }

        // 显示导入提示词对话框
        function showImportPromptDialog() {
            const dialogHtml = `
                <div class="dialog-overlay" id="importPromptDialog">
                    <div class="dialog-content">
                        <div class="dialog-title">📥 导入提示词</div>
                        <div class="dialog-body">
                            <div class="form-group">
                                <label class="form-label">选择JSON文件</label>
                                <input type="file" class="form-input" id="importPromptFile" accept=".json">
                                <div style="margin-top: 8px; font-size: 12px; color: rgba(255,255,255,0.6);">
                                    支持导出的提示词文件、提示词数组或单个提示词对象格式
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label" style="display: flex; justify-content: space-between; align-items: center;">
                                    覆盖现有提示词
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="importPromptOverwrite">
                                        <span class="slider"></span>
                                    </label>
                                </label>
                                <div style="margin-top: 8px; font-size: 12px; color: rgba(255,255,255,0.6);">
                                    开启后将更新已存在的同ID提示词，否则重命名导入
                                </div>
                            </div>
                        </div>
                        <div class="dialog-footer">
                            <button type="button" class="btn btn-secondary" onclick="closeImportPromptDialog()">取消</button>
                            <button type="button" class="btn btn-primary" onclick="importPromptsFromFile()">开始导入</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', dialogHtml);
        }

        // 关闭导入对话框
        function closeImportPromptDialog() {
            const dialog = document.getElementById('importPromptDialog');
            if (dialog) {
                dialog.remove();
            }
        }

        // 从文件导入提示词
        async function importPromptsFromFile() {
            const fileInput = document.getElementById('importPromptFile');
            const overwrite = document.getElementById('importPromptOverwrite').checked;
            
            if (!fileInput.files[0]) {
                alert('请选择文件');
                return;
            }

            try {
                const fileContent = await fileInput.files[0].text();
                let importData;
                
                try {
                    importData = JSON.parse(fileContent);
                } catch (parseError) {
                    throw new Error('JSON文件格式错误，请检查文件格式');
                }

                console.log('准备导入的提示词数据:', importData);

                const response = await makeAuthenticatedRequest('/admin/prompts/import', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        import_data: importData,
                        overwrite: overwrite
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    const results = result.results;
                    
                    let message = result.message || '导入完成！';
                    let messageType = 'success';
                    
                    // 如果有详细结果，添加更多信息
                    if (results && results.errors && results.errors.length > 0) {
                        message += '\n\n详细信息：';
                        results.errors.slice(0, 8).forEach((error, index) => {
                            message += `\n${index + 1}. ${error}`;
                        });
                        if (results.errors.length > 8) {
                            message += `\n... 还有 ${results.errors.length - 8} 个信息`;
                        }
                        
                        // 如果没有成功创建或更新任何提示词，改变消息类型
                        if (results.created === 0 && results.updated === 0) {
                            messageType = 'warning';
                        }
                    }
                    
                    showPromptMessage(message, messageType);
                    closeImportPromptDialog();
                    loadPrompts();
                } else {
                    const error = await response.json();
                    throw new Error(error.detail || '导入失败');
                }
            } catch (error) {
                console.error('提示词导入错误:', error);
                showPromptMessage(`导入失败：${error.message}`, 'error');
            }
        }

        // 查看提示词
        async function viewPrompt(promptId) {
            try {
                const response = await makeAuthenticatedRequest(`/admin/prompts/${promptId}`);
                if (response.ok) {
                    const data = await response.json();
                    showPromptViewDialog(data.prompt);
                }
            } catch (error) {
                showPromptMessage('获取提示词信息失败', 'error');
            }
        }

        // 显示提示词查看对话框
        function showPromptViewDialog(promptData) {
            const dialogHtml = `
                <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.8); z-index: 10000; display: flex; align-items: center; justify-content: center;" id="promptViewDialog">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(25px); border-radius: 20px; padding: 30px; width: 90%; max-width: 800px; max-height: 90vh; overflow-y: auto;">
                        <h3 style="margin-bottom: 20px;">查看提示词: ${promptData.name}</h3>
                        <div style="margin-bottom: 15px;">
                            <strong>描述:</strong> ${promptData.description || '无'}
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>分类:</strong> ${promptData.category || '无'} | 
                            <strong>作者:</strong> ${promptData.author || '无'} | 
                            <strong>版本:</strong> ${promptData.version || '无'}
                        </div>
                        <div style="margin-bottom: 15px;">
                            <strong>标签:</strong> ${(promptData.tags || []).join(', ') || '无'}
                        </div>
                        <div style="margin-bottom: 20px;">
                            <strong>内容:</strong>
                            <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px; white-space: pre-wrap; font-family: monospace; max-height: 300px; overflow-y: auto; margin-top: 10px;">${promptData.content}</div>
                        </div>
                        <div style="text-align: center;">
                            <button type="button" class="btn btn-secondary" onclick="closePromptViewDialog()">关闭</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', dialogHtml);
        }

        // 关闭提示词查看对话框
        function closePromptViewDialog() {
            const dialog = document.getElementById('promptViewDialog');
            if (dialog) {
                dialog.remove();
            }
        }

        // 显示提示词消息
        function showPromptMessage(message, type) {
            const messageDiv = document.getElementById('promptMessage');
            messageDiv.textContent = message;
            if (type === 'error') {
                messageDiv.className = 'error-message';
            } else if (type === 'warning') {
                messageDiv.className = 'warning-message';
            } else {
                messageDiv.className = 'success-message';
            }
            messageDiv.style.display = 'block';

            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }

        // 切换标签页时自动加载数据
        function switchTab(tab, event) {
            // 移除所有nav-item的active类
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 为当前导航项添加active类
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // 如果没有事件对象，通过选择器查找对应的导航项
                const activeNavItem = document.querySelector(`.nav-item[onclick*="'${tab}'"]`);
                if (activeNavItem) {
                    activeNavItem.classList.add('active');
                }
            }

            // 隐藏所有内容区域
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // 显示目标内容区域
            const targetSection = document.getElementById(tab + 'Section');
            if (targetSection) {
                targetSection.classList.add('active');
                
                // 根据不同的标签页加载相应的数据
                switch(tab) {
                    case 'stats':
                        loadStats();
                        break;
                    case 'characters':
                        loadCharacters();
                        break;
                    case 'prompts':
                        loadPrompts();
                        break;
                    case 'config':
                        loadConfig();
                        break;
                    case 'images':
                        loadImages();
                        break;
                }
            }
        }

    </script>
</body>
</html>